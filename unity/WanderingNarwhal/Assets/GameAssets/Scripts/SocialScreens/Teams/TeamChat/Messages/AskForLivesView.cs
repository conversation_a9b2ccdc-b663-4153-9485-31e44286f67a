using System;
using BBB;
using BBB.DI;
using BBB.Social.Chat;
using BBB.UI.Core;
using BebopBee.Core;
using Bebopbee.Core.Extensions.Unity;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using GameAssets.Scripts.UI.OverlayDialog;

namespace GameAssets.Scripts.SocialScreens.Teams.TeamChat.Messages
{
    public class AskForLivesView : ContextedUiBehaviour, IMessageView
    {
        private const string MissingName = "Missing Name";

        private static readonly int Help = Animator.StringToHash("Help");
        private static readonly int Hide = Animator.StringToHash("Hide");
        public const int MaxLivesHelp = 5;

        [SerializeField] private Animator _hideAnimator;
        [SerializeField] private Canvas _rootCanvas;
        [SerializeField] private RectTransform _root;
        [SerializeField] private Animator _helpAnimator;

        [SerializeField] private GameObject[] _senderInfoHolder;
        [SerializeField] private TextMeshProUGUI _name;
        [SerializeField] private AsyncAvatar _asyncAvatar;

        [SerializeField] private TextMeshProUGUI _progressText;
        [SerializeField] private Image _progressImage;
        [SerializeField] private float _fillingDelay = 0.2f;
        [SerializeField] private float _fillingTime = 0.5f;
        [SerializeField] private Ease _fillingEase = Ease.InOutSine;

        [SerializeField] private Button _helpButton;
        [SerializeField] private GameObject _buttonEnabledBg;
        [SerializeField] private GameObject _buttonDisabledBg;
        [SerializeField] private GameObject _buttonEnabledHolder;
        [SerializeField] private GameObject _buttonDisabledHolder;

        private IChatManager _chatManager;
        private IOverlayDialogManager _overlayDialogManager;
        private Action<ChatMessage, Action, Action<string>> _helpButtonClickedCallback;
        private TweenerCore<float, float, FloatOptions> _fillingTweener;
        private int _receivedHelp;
        private bool _loading;

        public ChatMessage Message { get; private set; }
        public RectTransform RectTransform => _root;
        public bool IsHeadless { get; private set; }

        protected override void Awake()
        {
            base.Awake();

            _helpButton.ReplaceOnClick(HelpButtonHandler);
            SetupProgressImage(0);
        }

        protected override void InitWithContextInternal(IContext context)
        {
            _chatManager = context.Resolve<IChatManager>();
            _overlayDialogManager = context.Resolve<IOverlayDialogManager>();
        }

        public void Setup(ChatMessage message, bool chatConnected, bool isHeadless, Action<ChatMessage, Action, Action<string>> helpButtonClickedCallback)
        {
            LazyInit();
            Message = message;
            IsHeadless = isHeadless;

            _senderInfoHolder.Enable(!isHeadless);
            SetupSenderInfo(message);

            _helpButtonClickedCallback = helpButtonClickedCallback;

            var alreadyReceivedLives = GetLivesProgress(message);
            SetupProgressImage(alreadyReceivedLives);
            SetConnectionState(chatConnected);
        }

        private void SetupSenderInfo(ChatMessage message)
        {
            var senderName = MissingName;
            if (!message.Sender.Name.IsNullOrEmpty())
            {
                senderName = message.Sender.Name;
            }

            _asyncAvatar.Setup(new AvatarInfo(message.Sender.Pic));

            if (_name == null)
                return;

            _name.text = senderName;
        }

        public void SetConnectionState(bool connected)
        {
            RefreshHelpButton();
        }

        private void RefreshHelpButton()
        {
            SetButtonInteractable(!_loading && _receivedHelp < MaxLivesHelp && !_chatManager.AlreadySentHelp(Message));
        }

        public static int GetLivesProgress(ChatMessage message)
        {
            return message.Helps != null ? Mathf.Min(message.Helps.Count, MaxLivesHelp) : 0;
        }

        public void SetVisible(bool visible)
        {
            _rootCanvas.enabled = visible;
        }

        public void Destroy()
        {
            if (gameObject.activeInHierarchy)
                _hideAnimator.SetTrigger(Hide);
            else
                HideAnimationCallback();
        }

        private void HideAnimationCallback()
        {
            Destroy(gameObject);
        }

        private void SetupProgressImage(int receivedHelp)
        {
            var progress = (float)receivedHelp / MaxLivesHelp;

            if (_receivedHelp < receivedHelp)
            {
                ResetTweener();

                var currentProgress = _progressImage.fillAmount;
                _fillingTweener = DOTween.To(() => currentProgress, (x) => { _progressImage.fillAmount = x; },
                    progress, _fillingTime).SetDelay(_fillingDelay).SetEase(_fillingEase).OnComplete(ResetTweener);

                Rx.Invoke(_fillingTime, x => { _progressText.text = $"{receivedHelp}/{MaxLivesHelp}"; });
            }
            else if (_receivedHelp != receivedHelp || _fillingTweener == null)
            {
                ResetTweener();
                _progressImage.fillAmount = progress;
            }

            _progressText.text = $"{receivedHelp}/{MaxLivesHelp}";
            _receivedHelp = receivedHelp;
        }

        private void ResetTweener()
        {
            if (_fillingTweener != null)
            {
                _fillingTweener.Kill();
                _fillingTweener = null;
            }
        }

        private void SetButtonInteractable(bool isInteractable)
        {
            if (_helpButton != null)
            {
                _buttonDisabledBg.SetActive(!isInteractable);
                _buttonDisabledHolder.SetActive(!isInteractable);

                _buttonEnabledBg.SetActive(isInteractable);
                _buttonEnabledHolder.SetActive(isInteractable);

                _helpButton.interactable = isInteractable;
            }
        }

        private void HelpButtonHandler()
        {
            _loading = true;
            RefreshHelpButton();
            int oldHelpCount = _receivedHelp;
            _helpAnimator.SetTrigger(Help);
            SetupProgressImage(oldHelpCount + 1);
            _helpButtonClickedCallback?.Invoke(Message, () =>
            {
                _loading = false;
                RefreshHelpButton();
            }, error =>
            {
                _loading = false;
                SetupProgressImage(oldHelpCount);
                RefreshHelpButton();

                if (_helpButton.isActiveAndEnabled)
                {
                    var _overlayDialogConfig = new OverlayDialogConfig();
                    _overlayDialogConfig.DisplayType = DisplayType.FloatingText;
                    _overlayDialogConfig.TargetTransform = _helpButton.transform;
                    _overlayDialogConfig.ShowBackground = true;
                    _overlayDialogConfig.TextToDisplay = error;
                    _overlayDialogManager.ShowOverlayDialog(_overlayDialogConfig);
                }
            });
        }
    }
}