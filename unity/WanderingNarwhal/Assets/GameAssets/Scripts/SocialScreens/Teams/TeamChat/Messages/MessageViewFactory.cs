using System;
using System.Collections.Generic;
using BBB;
using BBB.BrainCloud;
using BBB.DI;
using BBB.Core;
using BBB.Social.Chat;
using BBB.UI.Core;
using BebopBee;
using BBB.Chat;
using BBB.TeamEvents;
using Bebopbee.Core.Systems.RpcCommandManager;
using FBConfig;
using UnityEngine;
using UnityEngine.Profiling;
using UnityEngine.UI;

namespace GameAssets.Scripts.SocialScreens.Teams.TeamChat.Messages
{
    public class MessageViewFactory : ContextedUiBehaviour
    {
        private const string TeamsChatAnnouncementTeamCreatedInitialText = "TEAMS_CHAT_ANNOUNCEMENT_TEAM_CREATED_INITIAL_TEXT";
        private const string TeamsChatAnnouncementMemberJoinText = "TEAMS_CHAT_ANNOUNCEMENT_MEMBER_JOIN_TEXT";
        private const string TeamsChatAnnouncementMemberLeaveText = "TEAMS_CHAT_ANNOUNCEMENT_MEMBER_QUIT_TEXT";
        private const string TeamsChatAnnouncementMemberKickText = "TEAMS_CHAT_ANNOUNCEMENT_MEMBER_KICK_TEXT";
        private const string TeamsChatMemberKickInactiveText = "TEAMS_CHAT_MEMBER_KICK_INACTIVE_TEXT";
        private const string TeamsChatAnnouncementAdminAddedText = "TEAMS_CHAT_ANNOUNCEMENT_ADMIN_ADDED_TEXT";
        private const string TeamsChatAnnouncementAdminRemovedText = "TEAMS_CHAT_ANNOUNCEMENT_ADMIN_REMOVED_TEXT";

        private const string TeamsChatAnnouncementNameChanged = "TEAMS_CHAT_ANNOUNCEMENT_NAME_CHANGED";
        private const string TeamsChatAnnouncementDescriptionChanged = "TEAMS_CHAT_ANNOUNCEMENT_DESCRIPTION_CHANGED";
        private const string TeamsChatAnnouncementNameAndDescriptionChanged = "TEAMS_CHAT_ANNOUNCEMENT_NAME_AND_DESCRIPTION_CHANGED";
        private const string TeamsChatAnnouncementInfoChanged = "TEAMS_CHAT_ANNOUNCEMENT_INFO_CHANGED";
        private const string TeamsChatAnnouncementEmptyDescriptionFallback = "TEAMS_CHAT_ANNOUNCEMENT_EMPTY_DESCRIPTION_FALLBACK";
        private const string TeamsChatAdminTransferText = "TEAMS_CHAT_ADMIN_TRANSFER_TEXT";
        private const string TeamsChatIapClaimedText = "TEAMS_CHAT_IAP_CLAIMED_TEXT";

        private static readonly int NudgeTeamCoopLifetime = 1 * Util.SECONDS_IN_A_DAY;
        private static readonly int NudgeTeamVsTeamLifetime = 1 * Util.SECONDS_IN_A_DAY;

        [SerializeField] private RectTransform _listRoot;
        [SerializeField] private RectTransform _maskRect;
        [SerializeField] private ScrollRect _scrollRect;

        [Header("Local")]
        [SerializeField] private GameObject _localMessageViewPrefab;
        [SerializeField] private GameObject _localAskForLivesViewPrefab;
        [SerializeField] private GameObject _localNudgeTeamCoopMessageViewPrefab;
        [SerializeField] private GameObject _localNudgeTeamVsTeamMessageViewPrefab;

        [Header("External")]
        [SerializeField] private GameObject _messageViewPrefab;
        [SerializeField] private GameObject _askForLivesViewPrefab;
        [SerializeField] private GameObject _nudgeTeamCoopMessageViewPrefab;
        [SerializeField] private GameObject _nudgeTeamVsTeamMessageViewPrefab;

        [Header("System")]
        [SerializeField] private GameObject _createMessageViewPrefab;
        [SerializeField] private GameObject _joinMessageViewPrefab;
        [SerializeField] private GameObject _leaveMessageViewPrefab;
        [SerializeField] private GameObject _kickMessageViewPrefab;
        [SerializeField] private GameObject _adminToggledMessageViewPrefab;
        [SerializeField] private GameObject _iapSharedMessageViewPrefab;
        [SerializeField] private GameObject _teamInfoEditedMessageViewPrefab;
        [SerializeField] private GameObject _iapClaimedMessageViewPrefab;

        [SerializeField] private GameObject _loadingPrefab;

        private readonly List<IMessageView> _messageViewsCache = new();
        private readonly List<BaseMessageView> _baseMessageViews = new();

        private ILocalizationManager _localizationManager;
        private IChatManager _chatManager;
        private IAccountManager _accountManager;
        private ITimeController _timeController;
        private ITeamEventManager _teamEventManager;
        private BrainCloudManager _brainCloudManager;
        private IDictionary<string, IceBreakerConfig> _iceBreakerConfig;

        private Dictionary<MessageType, GameObject> _prefabBySystemMessageType;
        private Dictionary<MessageType, string> _textBySystemMessageType;

        private Action<ChatMessage, Action, Action<string>> _helpClickedCallback;
        private Action<ChatMessage, string, Dictionary<string, int>, Action, Action> _iapGiftClaimClickCallback;
        private Action<ChatMessage> _attachmentClickCallback;
        private Action<BaseMessageView> _messageClickCallback;
        private Action<Type> _nudgeTeamCoopClickedCallback;

        private Action _layoutRefreshCallback;
        private GameObject _loadingGameObject;
        private ChatMessage _previousMessage;
        private int _lastTimeMessageViewUpdateTime;

        protected override void InitWithContextInternal(IContext context)
        {
            _localizationManager = context.Resolve<ILocalizationManager>();
            _chatManager = context.Resolve<IChatManager>();
            _timeController = context.Resolve<ITimeController>();
            _accountManager = context.Resolve<IAccountManager>();
            _teamEventManager = context.Resolve<ITeamEventManager>();
            _brainCloudManager = context.Resolve<BrainCloudManager>();
            _iceBreakerConfig = context.Resolve<IConfig>().Get<FBConfig.IceBreakerConfig>();

            _prefabBySystemMessageType = new Dictionary<MessageType, GameObject>()
            {
                { MessageType.CreatedTeam, _createMessageViewPrefab },
                { MessageType.JoinedTeam, _joinMessageViewPrefab },
                { MessageType.LeftTeam, _leaveMessageViewPrefab },
                { MessageType.KickFromTeam, _kickMessageViewPrefab },
                { MessageType.KickInactiveFromTeam, _kickMessageViewPrefab },
                { MessageType.AdminToggled, _adminToggledMessageViewPrefab },
                { MessageType.TeamInfoEdited, _teamInfoEditedMessageViewPrefab },
                { MessageType.AdminTransfer, _kickMessageViewPrefab },
                { MessageType.IAPClaimed, _iapClaimedMessageViewPrefab },
            };

            _textBySystemMessageType = new Dictionary<MessageType, string>()
            {
                { MessageType.CreatedTeam, TeamsChatAnnouncementTeamCreatedInitialText },
                { MessageType.JoinedTeam, TeamsChatAnnouncementMemberJoinText },
                { MessageType.LeftTeam, TeamsChatAnnouncementMemberLeaveText },
            };
        }

        private void Update()
        {
            if (_scrollRect != null)
            {
                RefreshCullInternal();
            }

            var time = (int)Time.time;
            if (time > _lastTimeMessageViewUpdateTime)
            {
                UpdateMessageViews(time);
            }
        }

        private void RefreshCullInternal()
        {
            if (_messageViewsCache == null || _messageViewsCache.Count == 0)
                return;

            var contentY = _listRoot.anchoredPosition.y;
            var maskHeight = _maskRect.rect.height;

            foreach (var messageView in _messageViewsCache)
            {
                var rectTransform = messageView.RectTransform;
                var relativeHeight = -rectTransform.anchoredPosition.y - contentY;
                var height = rectTransform.sizeDelta.y;
                var pivot = rectTransform.pivot.y;
                var distanceFromMessageBottomToMaskTop = relativeHeight + height * pivot;
                var distanceFromMessageTopToMaskBottom = relativeHeight - height * (1f - pivot);

                messageView.SetVisible(distanceFromMessageBottomToMaskTop > 0 && distanceFromMessageTopToMaskBottom < maskHeight);
            }
        }

        private void UpdateMessageViews(int time)
        {
            foreach (var messageView in _baseMessageViews)
            {
                messageView.UpdateView();
            }

            _lastTimeMessageViewUpdateTime = time;
        }

        public void Setup(Action<ChatMessage, Action, Action<string>> helpClickCallback, Action<ChatMessage, string, Dictionary<string, int>, Action, Action> iapGiftClaimClickCallback,
            Action<Type> nudgeTeamCoopClickedCallback, Action<BaseMessageView> messageClickCallback, Action<ChatMessage> attachmentClickCallback,
            Action layoutRefreshCallback)
        {
            LazyInit();
            _helpClickedCallback = helpClickCallback;
            _iapGiftClaimClickCallback = iapGiftClaimClickCallback;
            _nudgeTeamCoopClickedCallback = nudgeTeamCoopClickedCallback;
            _messageClickCallback = messageClickCallback;
            _attachmentClickCallback = attachmentClickCallback;
            _layoutRefreshCallback = layoutRefreshCallback;
        }

        public void StartLoading()
        {
            RemoveLoading();
            Profiler.BeginSample($"Instantiate[{_loadingPrefab.name}]");
            _loadingGameObject = Instantiate(_loadingPrefab, _listRoot);
            Profiler.EndSample();
            _loadingGameObject.transform.SetAsLastSibling();
        }

        public void RemoveLoading()
        {
            if (_loadingGameObject != null)
            {
                Destroy(_loadingGameObject);
                _loadingGameObject = null;
            }
        }

        public void Clear()
        {
            _listRoot.RemoveAllActiveChilden();
            _messageViewsCache.Clear();
            _baseMessageViews.Clear();
            _previousMessage = null;
        }

        public void AddMessage(ChatMessage message, bool chatConnected)
        {
            if (message == null)
                return;

            var isLocal = _chatManager.IsLocalUser(message.Sender.Id);
            var messageType = message.GetMessageType();
            var isHeadless = _previousMessage != null && _previousMessage.Sender.Id == message.Sender.Id &&
                             _previousMessage.GetMessageType() is MessageType.Regular or MessageType.Attachment or MessageType.IceBreakerAnswer or MessageType.AskForLives or MessageType.NudgeTeamEvent
                                 or MessageType.NudgeTeamVsTeamEvent;

            switch (messageType)
            {
                case MessageType.AskForLives:
                {
                    var maxLives = AskForLivesView.GetLivesProgress(message);
                    if (maxLives >= AskForLivesView.MaxLivesHelp)
                        break;

                    var teamJoiningTime = _accountManager.Profile.CurrentTeam.JoinedAt;
                    if (message.CreatedAt < teamJoiningTime)
                        break;

                    var prefab = isLocal ? _localAskForLivesViewPrefab : _askForLivesViewPrefab;
                    Profiler.BeginSample($"Instantiate[{prefab.name}]");
                    var go = Instantiate(prefab, _listRoot);
                    Profiler.EndSample();
                    var messageView = go.GetComponent<AskForLivesView>();
                    if (messageView == null)
                    {
                        BDebug.LogError(LogCat.General, $"Missing AskForLivesView on prefab {go.name}");
                        return;
                    }

                    messageView.Setup(message, chatConnected, isHeadless, _helpClickedCallback);
                    go.SetActive(true);

                    _messageViewsCache.Add(messageView);
                    break;
                }
                case MessageType.NudgeTeamEvent:
                {
                    if (_timeController.CurrentTimeStamp() > message.CreatedAt + NudgeTeamCoopLifetime)
                        break;

                    var teamEvent = _teamEventManager.GetHighestPriorityEvent() as TeamCoopEvent;
                    if (teamEvent == null || !teamEvent.ShouldShowIcon() || teamEvent.Finished || teamEvent.Completed)
                        break;

                    var prefab = isLocal ? _localNudgeTeamCoopMessageViewPrefab : _nudgeTeamCoopMessageViewPrefab;
                    Profiler.BeginSample($"Instantiate[{prefab.name}]");
                    var go = Instantiate(prefab, _listRoot);
                    Profiler.EndSample();
                    var messageView = go.GetComponent<TeamCoopMessageView>();
                    if (messageView == null)
                    {
                        BDebug.LogError(LogCat.General, $"Missing TeamCoopMessageView on prefab {go.name}");
                        return;
                    }

                    messageView.Setup(message, isHeadless, _nudgeTeamCoopClickedCallback);
                    go.SetActive(true);

                    _messageViewsCache.Add(messageView);
                    break;
                }
                case MessageType.NudgeTeamVsTeamEvent:
                {
                    if (_timeController.CurrentTimeStamp() > message.CreatedAt + NudgeTeamVsTeamLifetime)
                        break;

                    var receiverUid = message.AdditionalProperties.GetStringProperty(ChatMessageAdditionalProperties.TeamVsTeamRecipientUid);
                    if (receiverUid != _brainCloudManager.ProfileId)
                        break;

                    var teamEvent = _teamEventManager.GetHighestPriorityEvent() as TeamVsTeamEvent;
                    if (teamEvent == null || !teamEvent.ShouldShowIcon() || teamEvent.Finished || teamEvent.Completed)
                        break;

                    var prefab = isLocal ? _localNudgeTeamVsTeamMessageViewPrefab : _nudgeTeamVsTeamMessageViewPrefab;
                    Profiler.BeginSample($"Instantiate[{prefab.name}]");
                    var go = Instantiate(prefab, _listRoot);
                    Profiler.EndSample();
                    var messageView = go.GetComponent<TeamVsTeamMessageView>();
                    if (messageView == null)
                    {
                        BDebug.LogError(LogCat.General, $"Missing TeamVsTeamEventMessageView on prefab {go.name}");
                        return;
                    }

                    messageView.Setup(message, isHeadless, _nudgeTeamCoopClickedCallback);
                    go.SetActive(true);

                    _messageViewsCache.Add(messageView);
                    break;
                }
                case MessageType.Regular or MessageType.Attachment or MessageType.IceBreakerAnswer:
                {
                    var prefab = isLocal ? _localMessageViewPrefab : _messageViewPrefab;

                    Profiler.BeginSample($"Instantiate[{prefab.name}]");
                    var go = Instantiate(prefab, _listRoot);
                    Profiler.EndSample();
                    var messageView = go.GetComponent<BaseMessageView>();
                    if (messageView == null)
                    {
                        BDebug.LogError(LogCat.General, $"Missing BaseMessageView on prefab {go.name}");
                        return;
                    }

                    messageView.Setup(message, messageType, isHeadless, isLocal);
                    messageView.SetupReactions(message);
                    messageView.SetupLayoutRefreshCallback(_layoutRefreshCallback);
                    messageView.SetupClickCallback(_messageClickCallback);
                    messageView.SetupAttachmentClickCallback(_attachmentClickCallback);

                    if (messageType == MessageType.IceBreakerAnswer)
                    {
                        var iceBreakerQuestionUid = message.AdditionalProperties.GetStringProperty(ChatMessageAdditionalProperties.IceBreakerQuestionUid);
                        if (!iceBreakerQuestionUid.IsNullOrEmpty())
                        {
                            if (_iceBreakerConfig.TryGetValue(iceBreakerQuestionUid, out var iceBreakerConfig))
                            {
                                messageView.SetupIceBreakerQuestion(iceBreakerConfig);
                            }
                        }
                    }

                    go.SetActive(true);

                    _messageViewsCache.Add(messageView);
                    _baseMessageViews.Add(messageView);
                    break;
                }
                case MessageType.CreatedTeam:
                case MessageType.JoinedTeam:
                case MessageType.LeftTeam:
                case MessageType.KickFromTeam:
                case MessageType.KickInactiveFromTeam:
                case MessageType.AdminToggled:
                case MessageType.TeamInfoEdited:
                case MessageType.AdminTransfer:
                {
                    Profiler.BeginSample($"Instantiate[{_prefabBySystemMessageType[messageType].name}]");
                    var go = Instantiate(_prefabBySystemMessageType[messageType], _listRoot);
                    Profiler.EndSample();
                    var messageView = go.GetComponent<SystemMessageView>();
                    if (messageView == null)
                    {
                        BDebug.LogError(LogCat.General, $"Missing SystemMessageView on prefab {go.name}");
                        return;
                    }

                    messageView.Setup(message);

                    if (messageType == MessageType.AdminToggled)
                    {
                        var adminName = message.AdditionalProperties.GetStringProperty(ChatMessageAdditionalProperties.AdminName);
                        var isAdmin = message.AdditionalProperties.GetBoolProperty(ChatMessageAdditionalProperties.IsAdmin);
                        var localizationId = isAdmin ? TeamsChatAnnouncementAdminAddedText : TeamsChatAnnouncementAdminRemovedText;

                        messageView.SetupText(_localizationManager.getLocalizedTextWithArgs(localizationId, adminName, message.Text));
                    }
                    else if (messageType == MessageType.KickFromTeam)
                    {
                        var adminName = message.AdditionalProperties.GetStringProperty(ChatMessageAdditionalProperties.AdminName);
                        messageView.SetupText(_localizationManager.getLocalizedTextWithArgs(TeamsChatAnnouncementMemberKickText, adminName, message.Text));
                    }
                    else if (messageType == MessageType.KickInactiveFromTeam)
                    {
                        messageView.SetupText(_localizationManager.getLocalizedTextWithArgs(TeamsChatMemberKickInactiveText, message.Text));
                    }
                    else if (messageType == MessageType.AdminTransfer)
                    {
                        messageView.SetupText(_localizationManager.getLocalizedTextWithArgs(TeamsChatAdminTransferText, message.Text));
                    }
                    else if (messageType == MessageType.TeamInfoEdited)
                    {
                        var adminName = message.AdditionalProperties.GetStringProperty(ChatMessageAdditionalProperties.AdminName);
                        var nameChanged = message.AdditionalProperties.HasStringProperty(ChatMessageAdditionalProperties.TeamNewName);
                        var descriptionChanged = message.AdditionalProperties.HasStringProperty(ChatMessageAdditionalProperties.TeamNewDescription);

                        var teamNewName = message.AdditionalProperties.GetStringProperty(ChatMessageAdditionalProperties.TeamNewName);
                        var teamNewDescription = message.AdditionalProperties.GetStringProperty(ChatMessageAdditionalProperties.TeamNewDescription);

                        if (teamNewDescription.IsNullOrEmpty())
                            teamNewDescription = _localizationManager.getLocalizedText(TeamsChatAnnouncementEmptyDescriptionFallback);

                        if (nameChanged && descriptionChanged)
                        {
                            messageView.SetupText(_localizationManager.getLocalizedTextWithArgs(TeamsChatAnnouncementNameAndDescriptionChanged, adminName, teamNewName, teamNewDescription));
                        }
                        else if (nameChanged)
                        {
                            messageView.SetupText(_localizationManager.getLocalizedTextWithArgs(TeamsChatAnnouncementNameChanged, adminName, teamNewName));
                        }
                        else if (descriptionChanged)
                        {
                            messageView.SetupText(_localizationManager.getLocalizedTextWithArgs(TeamsChatAnnouncementDescriptionChanged, adminName, teamNewDescription));
                        }
                        else
                        {
                            messageView.SetupText(_localizationManager.getLocalizedTextWithArgs(TeamsChatAnnouncementInfoChanged, adminName));
                        }
                    }
                    else
                    {
                        messageView.SetupText(_localizationManager.getLocalizedTextWithArgs(_textBySystemMessageType[messageType], message.Text));
                    }

                    go.SetActive(true);
                    _messageViewsCache.Add(messageView);
                    break;
                }
                case MessageType.IAPClaimed:
                {
                    Profiler.BeginSample($"Instantiate[{_prefabBySystemMessageType[messageType].name}]");
                    var go = Instantiate(_prefabBySystemMessageType[messageType], _listRoot);
                    Profiler.EndSample();
                    var messageView = go.GetComponent<SystemMessageView>();
                    if (messageView == null)
                    {
                        BDebug.LogError(LogCat.General, $"Missing SystemMessageView on prefab {go.name}");
                        return;
                    }

                    messageView.Setup(message);
                    
                    // Use Sender as claimer name and Text as buyer name
                    var claimerName = message.Sender.Name;
                    var buyerName = message.Text;
                    messageView.SetupText(_localizationManager.getLocalizedTextWithArgs(TeamsChatIapClaimedText, claimerName, buyerName));

                    go.SetActive(true);
                    _messageViewsCache.Add(messageView);
                    break;
                }
                case MessageType.IAPShared:
                {
                    var alreadyClaimed = !isLocal && _chatManager.AlreadyClaimedIap(message);
                    if (alreadyClaimed)
                        break;

                    var teamJoiningTime = _accountManager.Profile.CurrentTeam.JoinedAt;
                    if (message.CreatedAt < teamJoiningTime)
                        break;

                    Profiler.BeginSample($"Instantiate[{_iapSharedMessageViewPrefab.name}]");
                    var go = Instantiate(_iapSharedMessageViewPrefab, _listRoot);
                    Profiler.EndSample();
                    var messageView = go.GetComponent<IAPSharedMessageView>();
                    if (messageView == null)
                    {
                        BDebug.LogError(LogCat.General, $"Missing IAPSharedMessageView on prefab {go.name}");
                        return;
                    }

                    messageView.Setup(message, chatConnected, isLocal, _iapGiftClaimClickCallback);

                    go.SetActive(true);
                    _messageViewsCache.Add(messageView);

                    break;
                }
            }

            _previousMessage = message;
        }

        public void UpdateMessage(ChatMessage message, bool chatConnected)
        {
            var messageFound = false;
            BaseMessageView baseMessageView = null;
            AskForLivesView askForLivesView = null;
            IAPSharedMessageView iapSharedMessageView = null;

            foreach (var cachedMessageView in _messageViewsCache)
            {
                if (cachedMessageView.Message.Id != message.Id) continue;

                messageFound = true;

                baseMessageView = cachedMessageView as BaseMessageView;
                if (baseMessageView != null)
                    break;

                askForLivesView = cachedMessageView as AskForLivesView;
                if (askForLivesView != null)
                    break;

                iapSharedMessageView = cachedMessageView as IAPSharedMessageView;
                if (iapSharedMessageView != null)
                    break;
            }

            if (!messageFound)
            {
                if (message.GetMessageType() != MessageType.IAPShared || !_chatManager.AlreadyClaimedIap(message))
                {
                    BDebug.LogError(LogCat.General, "Can't update message because it was not cached");
                }

                return;
            }

            if (baseMessageView != null)
            {
                baseMessageView.Setup(message, message.GetMessageType(), baseMessageView.IsHeadless, baseMessageView.IsOwn);
                baseMessageView.SetupReactions(message, true);
            }
            else if (askForLivesView != null)
            {
                askForLivesView.Setup(message, chatConnected, askForLivesView.IsHeadless, _helpClickedCallback);
            }
            else if (iapSharedMessageView != null)
            {
                var alreadyClaimed = _chatManager.AlreadyClaimedIap(message);
                if (alreadyClaimed)
                {
                    DeleteMessage(message);
                }
            }
        }

        public void DeleteMessage(ChatMessage message)
        {
            var messageFound = false;

            foreach (var cachedMessageView in _messageViewsCache)
            {
                if (cachedMessageView.Message.Id != message.Id) continue;

                messageFound = true;
                break;
            }

            if (!messageFound)
            {
                if (message.GetMessageType() != MessageType.IAPShared || !_chatManager.AlreadyClaimedIap(message))
                {
                    BDebug.LogError(LogCat.General, "Can't update message because it was not cached");
                }

                return;
            }

            var messageView = _messageViewsCache.Find(x => x.Message.Id == message.Id);

            var currentMessageIndex = _messageViewsCache.IndexOf(messageView);
            var previousMessageView = currentMessageIndex > 0 ? _messageViewsCache[currentMessageIndex - 1] : null;
            var nextMessageView = currentMessageIndex < _messageViewsCache.Count - 1 ? _messageViewsCache[currentMessageIndex + 1] : null;

            // if current message is the last one then previous message will change
            if (nextMessageView == null)
                _previousMessage = previousMessageView?.Message;

            _messageViewsCache.Remove(messageView);

            switch (messageView)
            {
                case BaseMessageView baseMessageView:
                {
                    // if current message with head and next message is from the same person then we need to replace that message
                    var withHead = !baseMessageView.IsHeadless;
                    if (withHead && nextMessageView != null && nextMessageView.Message.Sender.Id == message.Sender.Id)
                    {
                        // it can be attachment so we do extra check before replacement
                        if (nextMessageView is BaseMessageView nextBaseMessageView && nextBaseMessageView.IsHeadless)
                        {
                            nextBaseMessageView.Setup(nextBaseMessageView.Message, nextBaseMessageView.Message.GetMessageType(), false, nextBaseMessageView.IsOwn);
                            _layoutRefreshCallback?.Invoke();
                        }
                    }

                    _baseMessageViews.Remove(baseMessageView);
                    baseMessageView.Destroy();
                    break;
                }
                case AskForLivesView askForLivesView:
                    askForLivesView.Destroy();
                    break;
                case IAPSharedMessageView iapSharedMessageView:
                    iapSharedMessageView.Destroy();
                    break;
            }
        }

        public void SetConnectionState(bool connected)
        {
            foreach (var message in _messageViewsCache)
            {
                message.SetConnectionState(connected);
            }
        }


    }
}