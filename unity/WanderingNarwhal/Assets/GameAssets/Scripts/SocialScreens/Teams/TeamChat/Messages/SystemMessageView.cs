using BBB;
using BBB.Social.Chat;
using Kyub.EmojiSearch.UI;
using UnityEngine;

namespace GameAssets.Scripts.SocialScreens.Teams.TeamChat.Messages
{
    public class SystemMessageView : BbbMonoBehaviour, IMessageView
    {
        [SerializeField] private Canvas _rootCanvas;
        [SerializeField] private RectTransform _root;
        [SerializeField] private TMP_EmojiTextUGUI _messageText;

        public RectTransform RectTransform => _root;
        public ChatMessage Message { get; private set; }

        public void Setup(ChatMessage message)
        {
            Message = message;
        }

        public void SetupText(string text)
        {
            _messageText.text = text;
        }

        public void SetVisible(bool visible)
        {
            _rootCanvas.enabled = visible;
        }

        public void SetConnectionState(bool connected) { }

        public void Destroy()
        {
            Destroy(gameObject);
        }
    }
}