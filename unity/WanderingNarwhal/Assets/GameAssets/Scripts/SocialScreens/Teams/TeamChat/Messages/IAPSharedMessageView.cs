using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BBB.UI;
using BBB.UI.Core;
using BBB.Chat;
using FBConfig;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using BBB.Social.Chat;
using Bebopbee.Core.Extensions.Unity;

namespace GameAssets.Scripts.SocialScreens.Teams.TeamChat.Messages
{
    public class IAPSharedMessageView : ContextedUiBehaviour, IMessageView
    {
        private static readonly int Hide = Animator.StringToHash("Hide");

        [SerializeField] private Animator _hideAnimator;
        [SerializeField] private Canvas _rootCanvas;
        [SerializeField] private RectTransform _root;
        [SerializeField] private TextMeshProUGUI _buyerName;
        [SerializeField] private LocalizedTextPro _packDescriptionText;
        [SerializeField] private UIRewardComponent _rewardComponent;
        [SerializeField] private GameObject[] _senderHolders;
        [SerializeField] private GameObject[] _receiverHolders;
        
        [SerializeField] private Button _claimButton;
        [SerializeField] private GameObject _buttonEnabledHolder;
        [SerializeField] private GameObject _buttonLoadingHolder;

        private Action<ChatMessage, string, Dictionary<string, int>, Action, Action> _iapGiftClaimClickCallback;
        private IDictionary<string, IAPStoreMarketItemConfig> _iapStoreMarketItemConfig;
        private ILocalizationManager _localizationManager;
        private Dictionary<string, int> _reward;
        private string _productUid;
        private bool _loading;

        public RectTransform RectTransform => _root;
        public ChatMessage Message { get; private set; }

        protected override void InitWithContextInternal(IContext context)
        {
            _iapStoreMarketItemConfig = context.Resolve<IConfig>().Get<IAPStoreMarketItemConfig>();
            _localizationManager = context.Resolve<ILocalizationManager>();

            _rewardComponent.Init(context);

            _claimButton.ReplaceOnClick(ClaimButtonHandler);
        }

        public void Setup(ChatMessage message, bool chatConnected, bool isOwn, Action<ChatMessage, string, Dictionary<string, int>, Action, Action> iapGiftClaimClickCallback)
        {
            LazyInit();
            Message = message;
            _iapGiftClaimClickCallback = iapGiftClaimClickCallback;

            var buyerName = message.Text;
            _buyerName.text = buyerName;

            _senderHolders.Enable(isOwn);
            _receiverHolders.Enable(!isOwn);
            
            _productUid = message.AdditionalProperties.GetStringProperty(ChatMessageAdditionalProperties.ProductID);

            _reward = RewardsUtility.RewardStringToDict("regular=25");
            if (_iapStoreMarketItemConfig.TryGetValue(_productUid, out var product))
            {
                _packDescriptionText.FormatSetArgs(_localizationManager.getLocalizedText(product.Name));

                if (!product.SocialSharedReward.IsNullOrEmpty())
                {
                    _reward = RewardsUtility.RewardStringToDict(product.SocialSharedReward).FilterRewards();
                }
            }
            else
            {
                _packDescriptionText.FormatSetArgs(_localizationManager.getLocalizedText(_productUid));
                Debug.LogError($"Couldn't find iap product with uid: {_productUid}");
            }

            _rewardComponent.SetupReward(_reward);
            SetConnectionState(chatConnected);
        }

        public void SetConnectionState(bool connected)
        {
            RefreshClaimButton();
        }

        private void RefreshClaimButton()
        {
            if (_claimButton != null)
            {
                _buttonEnabledHolder.SetActive(!_loading);
                _buttonLoadingHolder.SetActive(_loading);
                _claimButton.interactable = !_loading;
            }
        }

        private void ClaimButtonHandler()
        {
            _loading = true;
            RefreshClaimButton();
            _iapGiftClaimClickCallback?.Invoke(Message, _productUid, _reward, null, () =>
            {
                _loading = false;
                RefreshClaimButton();
            });
        }

        public void SetVisible(bool visible)
        {
            _rootCanvas.enabled = visible;
        }

        public void Destroy()
        {
            _claimButton.interactable = false;
            if (gameObject.activeInHierarchy)
                _hideAnimator.SetTrigger(Hide);
            else
                HideAnimationCallback();
        }

        private void HideAnimationCallback()
        {
            Destroy(gameObject);
        }
    }
}