using System;
using System.Collections.Generic;
using BBB;

namespace GameAssets.Scripts.SocialScreens.Teams.TeamInfo
{
    public class TeamPublicInfo
    {
        private const int TeamMembersLimit = 50;
        public const string CreateTeamTypePublic = "CREATE_TEAM_TEAM_TYPE_PUBLIC";
        public const string CreateTeamTypePrivate = "CREATE_TEAM_TEAM_TYPE_PRIVATE";

        public string Id { get; private set; }
        public string StreamId { get; set; }
        public string Name { get; private set; }
        public string Description { get; private set; }
        public string Thumbnail { get; private set; }
        public int MembersNumber { get; private set; }
        public int MembersLimit { get; private set; }
        public int Scores { get; private set; }
        public int RequiredLevel { get; private set; }
        public TeamActivity Activity { get; private set; }
        public TeamType TeamType { get; set; }
        public List<TeamMemberData> Members { get; private set; }
        public TeamData TeamData { get; private set; }

        public void SetupTeamData(TeamData teamData)
        {
            teamData.Members ??= new List<TeamMemberData>();

            TeamData = teamData;
            Id = teamData.TeamUid;
            Name = teamData.Name;
            Description = teamData.Description;
            Thumbnail = teamData.Icon;
            MembersNumber = teamData.Members.Count;
            MembersLimit = TeamMembersLimit;

            Scores = 0;
            foreach (var member in teamData.Members)
            {
                Scores += member.Trophies;
            }

            RequiredLevel = teamData.RequiredLevel;
            TeamType = (TeamType)teamData.TeamType;
            Activity = (TeamActivity)teamData.Activity;
            Members = teamData.Members;
        }


        public string GetActivityTextId()
        {
            return Activity switch
            {
                TeamActivity.Low => "TEAM_ACTIVITY_LOW",
                TeamActivity.Medium => "TEAM_ACTIVITY_MEDIUM",
                TeamActivity.High => "TEAM_ACTIVITY_HIGH",
                _ => throw new ArgumentOutOfRangeException()
            };
        }

        public string GetTeamTypeTextId()
        {
            return TeamType switch
            {
                TeamType.Public => CreateTeamTypePublic,
                TeamType.Private => CreateTeamTypePrivate,
                _ => throw new ArgumentOutOfRangeException()
            };
        }
    }
}