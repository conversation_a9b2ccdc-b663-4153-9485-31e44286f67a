using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using BBB;
using BBB.Core.Analytics;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.Screens;
using BBB.Social.Chat;
using BBB.UI.IAP.Controllers;
using BBB.Wallet;
using BebopBee;
using GameAssets.Scripts.SocialScreens.Teams.IceBreaker;
using GameAssets.Scripts.SocialScreens.Teams.NoTeam;
using GameAssets.Scripts.SocialScreens.Teams.NoTeam.CreateTeam;
using GameAssets.Scripts.SocialScreens.Teams.Screens;
using GameAssets.Scripts.SocialScreens.Teams.Screens.LogoSelectionScreen;
using BBB.Chat;
using BBB.Core;
using BBB.Navigation;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.SocialScreens.Teams.TeamChat;
using GameAssets.Scripts.SocialScreens.Teams.TeamChat.Messages;
using GameAssets.Scripts.SocialScreens.Teams.TeamInfo;
using GameAssets.Scripts.UI.OverlayDialog;
using GameAssets.Scripts.Utils;
using UnityEngine;

namespace GameAssets.Scripts.SocialScreens.Teams
{
    public class SocialViewPresenter : ModalsViewPresenter, ISocialViewPresenter
    {
        private const string TeamsLeaveTitle = "TEAMS_LEAVE_TITLE";
        private const string TeamsLeaveDescription = "TEAMS_LEAVE_DESCRIPTION";
        private const string TeamsLeaveConfirm = "TEAMS_LEAVE_CONFIRM";
        private const string TeamsLeaveDecline = "TEAMS_LEAVE_DECLINE";
        
        private const string ResignTitle = "RESIGN_TITLE";
        private const string ResignDescription = "RESIGN_DESCRIPTION";
        private const string ResignConfirm = "RESIGN_CONFIRM";
        private const string ResignDecline = "RESIGN_DECLINE";

        private const string KickDialog1Title = "KICK_DIALOG1_TITLE";
        private const string KickBanDialog1Title = "KICK_BAN_DIALOG1_TITLE";
        private const string KickDialog1Description = "KICK_DIALOG1_DESCRIPTION";
        private const string KickBanDialog1Description = "KICK_BAN_DIALOG1_DESCRIPTION";
        private const string KickDialog1Yes = "KICK_DIALOG1_YES";
        private const string KickDialog1No = "KICK_DIALOG1_NO";
        private const string KickDialog2Title = "KICK_DIALOG2_TITLE";
        private const string KickDialog2Description = "KICK_DIALOG2_DESCRIPTION";
        private const string KickDialog2Yes = "KICK_DIALOG2_YES";
        private const string KickDialog2No = "KICK_DIALOG2_NO";

        private const string AdminDialog1Title = "ADMIN_DIALOG1_TITLE";
        private const string AdminDialog1Description = "ADMIN_DIALOG1_DESCRIPTION";
        private const string AdminDialog1Yes = "ADMIN_DIALOG1_YES";
        private const string AdminDialog1No = "ADMIN_DIALOG1_NO";

        private const string AdminDialog2Title = "ADMIN_DIALOG2_TITLE";
        private const string AdminDialog2Description = "ADMIN_DIALOG2_DESCRIPTION";
        private const string AdminDialog2Yes = "ADMIN_DIALOG2_YES";
        private const string AdminDialog2No = "ADMIN_DIALOG2_NO";

        private const string SEND_HELP_NO_INTERNET_ERROR = "SEND_HELP_NO_INTERNET_ERROR";
        private const string SEND_HELP_SERVER_ERROR = "SEND_HELP_SERVER_ERROR";

        private const int UploadFileSizeLimit = 50 * 1024 * 1024; //50 MB

        private const float ChatConnectionRetryDelay = 5f;
        private const float ChatConnectionRetryShortDelay = 1f;

        [SerializeField] private TeamsLogoConfig _teamsLogoConfig;

        [SerializeField] private GameObject _loaderHolder;
        [SerializeField] private BrowseTeamView _browseTeamView;
        [SerializeField] private TeamChatView _teamChatView;
        [SerializeField] private TeamInfoView _teamInfoView;
        [SerializeField] private NoConnectionView _noConnectionView;
        [SerializeField] private ErrorScreen _errorScreen;
        [SerializeField] private DialogScreen _dialogScreen;
        [SerializeField] private TeamEditScreen _teamEditScreen;
        [SerializeField] private IceBreakerScreen _iceBreakerScreen;
        [SerializeField] private IceBreakerPanel _iceBreakerPanel;
        [SerializeField] private ActivityRewardsManager _activityRewardsManager;
        [SerializeField] private Transform _floatingTextAnchor;
        [SerializeField] private OverlayDialogConfig _overlayDialogConfig;

        private ISocialManager _socialManager;
        private IWalletManager _walletManager;
        private IUIWalletManager _uiWalletManager;
        private IAccountManager _accountManager;
        private IModalsBuilder _modalsBuilder;
        private Coroutine _waitForMediaPickerCoroutine;
        private ILocalizationManager _localizationManager;
        private IceBreakerManager _iceBreakerManager;
        private IChatManager _chatManager;
        private IOverlayDialogManager _overlayDialogManager;
        private ILocationManager _locationManager;
        private ILevelsOrderingManager _levelsOrderingManager;

        private Coroutine _chatConnectionRetryCoroutine;

        public bool IsLoading => _loaderHolder.activeSelf;

        protected override void OnContextInitialized(IContext context)
        {
            base.OnContextInitialized(context);
            _socialManager = context.Resolve<ISocialManager>();
            _walletManager = context.Resolve<IWalletManager>();
            _uiWalletManager = context.Resolve<IUIWalletManager>();
            _accountManager = context.Resolve<IAccountManager>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _localizationManager = context.Resolve<ILocalizationManager>();
            _iceBreakerManager = context.Resolve<IceBreakerManager>();
            _chatManager = context.Resolve<IChatManager>();
            _overlayDialogManager = context.Resolve<IOverlayDialogManager>();
            _locationManager = context.Resolve<ILocationManager>();
            _levelsOrderingManager = context.Resolve<ILevelsOrderingManager>();

            _teamsLogoConfig.Validate();
            _teamChatView.ShowAttachmentLoading(false);
        }

        protected override void OnShow()
        {
            base.OnShow();

            Subscribe();

            _browseTeamView.Setup(
                BrowseTeamsHandler,
                SearchTeamHandler,
                CreateTeamRequestHandler,
                ShowTeamInfoHandler, _socialManager.BrowsedTeams);

            _teamChatView.Setup(
                SendMessageRequestHandler,
                AskForLivesRequestHandler,
                HelpButtonClickedHandler,
                ClaimIapClickedHandler,
                GalleryRequestHandler,
                ShowCurrentTeamInfoHandler,
                ReactionSelectedHandler,
                DeleteMessageHandler,
                FlagMessageHandler,
                NudgeTeamEventRequestHandler);

            _teamInfoView.Setup(_chatManager.IsLocalUser, CloseTeamInfoHandler, EditTeamButtonHandler, JoinTeamButtonHandler, LeaveTeamButtonHandler, ResignButtonHandler, AdminHandler, KickHandler, ShowLevelPromptOverlayDialog);

            _errorScreen.Hide();
            _dialogScreen.Hide();
            _iceBreakerScreen.Hide();
            _iceBreakerPanel.Hide();
            _teamEditScreen.Hide();

            if (TryHandleConnectionProblem(true)) return;

            ResetViews();

            if (_accountManager.IsInTeam)
            {
                _teamChatView.SetupCurrentTeam(_socialManager.CurrentTeam);
                _teamChatView.Show();
                UpdateChatConnection(true);

                _socialManager.MarkAllAsRead();

                var todayQuestion = _iceBreakerManager.GetTodayQuestion();
                if (todayQuestion != null)
                {
                    _iceBreakerPanel.Setup(todayQuestion, IceBreakerPanelClicked);
                    _iceBreakerPanel.ShowHighlight(!_iceBreakerManager.IsTodayQuestionAnswered() && !todayQuestion.IsShown);
                    _iceBreakerPanel.Show();
                }
            }
            else
            {
                RefreshTeamState();
            }
        }

        private void ResetViews()
        {
            _browseTeamView.Hide();
            _teamChatView.Hide();
            _teamInfoView.Hide();
            _noConnectionView.Hide();
        }

        private void ShowNoConnectionView()
        {
            ResetViews();
            _noConnectionView.Show();
        }

        private void IceBreakerPanelClicked()
        {
            var todayQuestion = _iceBreakerManager.GetTodayQuestion();
            if (todayQuestion != null && !_iceBreakerManager.IsTodayQuestionAnswered())
            {
                if (TryHandleConnectionProblem())
                    return;

                _iceBreakerScreen.Setup(todayQuestion, IceBreakerAnsweredHandler);
                _iceBreakerScreen.Show();
                _iceBreakerPanel.ShowHighlight(false);
            }
            else
            {
                _iceBreakerPanel.ShowAlreadyAnsweredText();
            }
        }

        private void IceBreakerAnsweredHandler(IceBreakerQuestion iceBreakerQuestion, string answerText)
        {
            if (TryHandleConnectionProblem())
                return;

            if (iceBreakerQuestion.Reward != null)
            {
                var transaction = new Transaction()
                    .AddTag(TransactionTag.Teams)
                    .SetAnalyticsData(CurrencyFlow.Social.Name, CurrencyFlow.Social.IceBreaker, iceBreakerQuestion.Uid)
                    .Earn(iceBreakerQuestion.Reward);

                _walletManager.TransactionController.MakeTransaction(transaction);
                _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.Teams);
                _iceBreakerManager.MarkAsAnswered(iceBreakerQuestion);
            }
            else
            {
                _iceBreakerManager.MarkAsAnswered(iceBreakerQuestion);
            }

            var additionalProperties = new Dictionary<string, object> { { ChatMessageAdditionalProperties.IceBreakerQuestionUid, iceBreakerQuestion.Uid } };
            _chatManager.SendMessage(new ChatMessageRequest()
            {
                MessageType = MessageType.IceBreakerAnswer.ToString(),
                Text = answerText,
                AdditionalProperties = additionalProperties,
            });
        }

        private void BrowseTeamsHandler(Action onBrowsingStart, Action<List<TeamPublicInfo>> callback)
        {
            if (TryHandleConnectionProblem()) return;

            onBrowsingStart.SafeInvoke();
            _socialManager.BrowseTeams((teams, success, error) =>
            {
                callback.SafeInvoke(teams);

                if (!success)
                {
                    _errorScreen.SetupError(error);
                }
            });
        }

        private void SearchTeamHandler(string searchQuery, Action onSearchStart, Action<List<TeamPublicInfo>> callback)
        {
            if (TryHandleConnectionProblem()) return;

            onSearchStart.SafeInvoke();
            _socialManager.SearchTeams(searchQuery, (teams, success, error) =>
            {
                callback.SafeInvoke(teams);

                if (!success)
                    _errorScreen.SetupError(error);
            });
        }

        private void RefreshTeamState()
        {
            _errorScreen.Hide();
            if (TryHandleConnectionProblem(true)) return;

            ResetViews();
            if (_accountManager.IsInTeam)
            {
                _teamChatView.SetupCurrentTeam(_socialManager.CurrentTeam);
                _teamChatView.Show();
                _teamChatView.ShowChatLoading();
                _teamChatView.RefreshMessages();
            }
            else
            {
                _browseTeamView.Show();
            }
        }

        protected override void OnHide()
        {
            _chatManager.IsTyping = false;
            ResetViews();
            _errorScreen.Hide();
            _loaderHolder.SetActive(false);

            if (_waitForMediaPickerCoroutine != null)
            {
                StopCoroutine(_waitForMediaPickerCoroutine);
                _waitForMediaPickerCoroutine = null;
            }

            base.OnHide();
            Unsubscribe();
        }

        private void Subscribe()
        {
            Unsubscribe();

            _socialManager.CurrentTeamFetched += CurrentTeamFetchedHandler;

            _chatManager.ChatMessageAdded += ChatMessagesAddedHandler;
            _chatManager.ChatMessageUpdated += ChatMessagesUpdatedHandler;
            _chatManager.ChatMessageDeleted += ChatMessagesDeletedHandler;
            _chatManager.ConnectedToChatFailure += UpdateChatConnectionFailed;
        }

        private void Unsubscribe()
        {
            _socialManager.CurrentTeamFetched -= CurrentTeamFetchedHandler;

            _chatManager.ChatMessageAdded -= ChatMessagesAddedHandler;
            _chatManager.ChatMessageUpdated -= ChatMessagesUpdatedHandler;
            _chatManager.ChatMessageDeleted -= ChatMessagesDeletedHandler;
            _chatManager.ConnectedToChatFailure -= UpdateChatConnectionFailed;

            UnsubscribeChatConnect();
            UnsubscribeChatUpdateSuccess();
        }

        private void SubscribeChatConnect()
        {
            UnsubscribeChatConnect();
            _chatManager.ConnectedToChatSuccess += ConnectedToChatHandler;
        }

        private void UnsubscribeChatConnect()
        {
            _chatManager.ConnectedToChatSuccess -= ConnectedToChatHandler;
        }

        private void SubscribeChatUpdateSuccess()
        {
            UnsubscribeChatUpdateSuccess();
            _chatManager.ConnectedToChatSuccess += UpdateChatConnectionComplete;
        }

        private void UnsubscribeChatUpdateSuccess()
        {
            _chatManager.ConnectedToChatSuccess -= UpdateChatConnectionComplete;
        }

        private void CurrentTeamFetchedHandler()
        {
            if (_accountManager.IsInTeam)
            {
                if (_teamInfoView.IsShown)
                {
                    ShowCurrentTeamInfoHandler();
                }
            }
            else
            {
                RefreshTeamState();
            }
        }

        private void ShowCurrentTeamInfoHandler()
        {
            if (TryHandleConnectionProblem()) return;

            if (_socialManager.CurrentTeam != null)
            {
                var teamPublicInfo = new TeamPublicInfo();
                teamPublicInfo.SetupTeamData(_socialManager.CurrentTeam);
                _teamInfoView.ShowTeamInfo(teamPublicInfo, true, true);
            }
            else
            {
                Debug.LogError($"Trying to show own team but it not set, can't show");
            }
        }

        private void ShowTeamInfoHandler(TeamPublicInfo teamPublicInfo)
        {
            if (TryHandleConnectionProblem()) return;

            var highestPassedLevelUid = _locationManager.MainProgressionLocation.GetHighestPassedLevelUid();
            if (!_levelsOrderingManager.TryParseLevelUid(highestPassedLevelUid, out var _, out var levelNumber))
            {
                BDebug.LogError(LogCat.Player,$"Couldn't find level state with uid: {highestPassedLevelUid}");
                levelNumber = 0;
            }

            _teamInfoView.ShowTeamInfo(teamPublicInfo, false, levelNumber >= teamPublicInfo.RequiredLevel);
        }

        private void ShowLevelPromptOverlayDialog(OverlayDialogConfig config)
        {
            _overlayDialogManager.ShowOverlayDialog(config);
        }

        private void GalleryRequestHandler()
        {
            _teamChatView.ShowAttachmentLoading(true);
            if (_waitForMediaPickerCoroutine != null)
                StopCoroutine(_waitForMediaPickerCoroutine);

            _waitForMediaPickerCoroutine = StartCoroutine(WaitForMediaPicker());
        }

        private IEnumerator WaitForMediaPicker()
        {
            var startTime = Time.realtimeSinceStartup;
            while (NativeGallery.IsMediaPickerBusy())
            {
                if (Time.realtimeSinceStartup > startTime + 0.5f)
                {
                    _loaderHolder.SetActive(true);
                }

                yield return null;
            }

            _loaderHolder.SetActive(false);
            _waitForMediaPickerCoroutine = null;
            GetImageFromGallery();
        }

        private void GetImageFromGallery()
        {
            var permission = NativeGallery.CheckPermission(NativeGallery.PermissionType.Read, NativeGallery.MediaType.Image | NativeGallery.MediaType.Video);
            switch (permission)
            {
                case NativeGallery.Permission.Granted:
                    GetMixedMediaFromGallery();
                    break;
                case NativeGallery.Permission.ShouldAsk:
                    permission = NativeGallery.RequestPermission(NativeGallery.PermissionType.Read, NativeGallery.MediaType.Image | NativeGallery.MediaType.Video);
                    if (permission == NativeGallery.Permission.Granted)
                        GetMixedMediaFromGallery();
                    else
                        _teamChatView.ShowAttachmentLoading(false);
                    break;
                case NativeGallery.Permission.Denied:
                    //we don't have permission and we can't ask the user for permission. In this case, user has to give the permission from Settings.
                    if (NativeGallery.CanOpenSettings())
                        NativeGallery.OpenSettings();
                    _teamChatView.ShowAttachmentLoading(false);
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

        private void GetMixedMediaFromGallery()
        {
            NativeGallery.GetMixedMediaFromGallery(GetMediaFromGalleryHandler, NativeGallery.MediaType.Image | NativeGallery.MediaType.Video);
        }

        private void GetMediaFromGalleryHandler(string path)
        {
            if (!path.IsNullOrEmpty())
            {
                if (new FileInfo(path).Length > UploadFileSizeLimit)
                {
                    // if exceeds 100mb then discard
                    _errorScreen.SetupError(SocialErrors.AttachmentFileSizeLimit);
                    _teamChatView.ShowAttachmentLoading(false);
                    return;
                }

                var mediaType = NativeGallery.GetMediaTypeOfFile(path);
                if (mediaType == NativeGallery.MediaType.Image)
                {
                    if (Path.GetExtension(path) == ".gif")
                    {
                        Action<ChatMessageAttachementUploaded[]> success = responses =>
                        {
                            string assetUrl;
                            string thumbUrl;
                            switch (responses.Length)
                            {
                                case 1:
                                    assetUrl = responses[0].AssetUrl;
                                    thumbUrl = assetUrl;
                                    break;
                                case 2:
                                    assetUrl = responses[1].AssetUrl;
                                    thumbUrl = responses[0].AssetUrl;
                                    break;
                                default:
                                    _errorScreen.SetupErrorRaw(_localizationManager.getLocalizedTextWithArgs(SocialErrors.AttachmentFileError, path));
                                    _teamChatView.ShowAttachmentLoading(false);
                                    return;
                            }

                            _activityRewardsManager.TryRewardAttachmentMessage();
                            _chatManager.SendMessage(new ChatMessageRequest()
                            {
                                MessageType = MessageType.Attachment.ToString(),
                                Text = "",
                                Attachments = new List<ChatMessageAttachementRequest>()
                                {
                                    new()
                                    {
                                        AssetUrl = assetUrl,
                                        ThumbUrl = thumbUrl,
                                        Type = "gif",
                                    }
                                }
                            }, () => { _teamChatView.ShowAttachmentLoading(false); });
                        };
                        Action failure = () =>
                        {
                            _errorScreen.SetupError(SocialErrors.TeamsImageUploadError);
                            _teamChatView.ShowAttachmentLoading(false);
                            return;
                        };
                        _chatManager.UploadGifToChat(path, success, failure);
                    }
                    else
                    {
                        var texture = NativeGallery.LoadImageAtPath(path, generateMipmaps: false);
                        if (texture == null)
                        {
                            _errorScreen.SetupError(_localizationManager.getLocalizedTextWithArgs(SocialErrors.AttachmentFileError, path));
                            _teamChatView.ShowAttachmentLoading(false);
                            return;
                        }

                        Destroy(texture);
                        Resources.UnloadUnusedAssets();

                        Action<ChatMessageAttachementUploaded> success = response =>
                        {
                            // Apply image orientation only for ios 15 only (14 and 16 works without it)
                            var additionalProperties = new Dictionary<string, object>();

                            if (Application.platform == RuntimePlatform.IPhonePlayer)
                            {
#if UNITY_IOS
                                var version = Version.Parse(UnityEngine.iOS.Device.systemVersion);
                                if (version.Major == 15)
                                {
                                    var imageProperties = NativeGallery.GetImageProperties(path);
                                    additionalProperties.Add(ChatMessageAdditionalProperties.ImageOrientation, (int)imageProperties.orientation);
                                }
#endif
                            }

                            _activityRewardsManager.TryRewardAttachmentMessage();
                            _chatManager.SendMessage(new ChatMessageRequest()
                            {
                                MessageType = MessageType.Attachment.ToString(),
                                Text = "",
                                Attachments = new List<ChatMessageAttachementRequest>()
                                {
                                    new()
                                    {
                                        AssetUrl = response.AssetUrl,
                                        ThumbUrl = response.AssetUrl,
                                        Type = "image",
                                        AdditionalProperties = additionalProperties,
                                    }
                                }
                            }, () => { _teamChatView.ShowAttachmentLoading(false); });
                        };
                        Action failure = () =>
                        {
                            _errorScreen.SetupError(SocialErrors.TeamsImageUploadError);
                            _teamChatView.ShowAttachmentLoading(false);
                            return;
                        };

                        _chatManager.UploadFileToChat(path, success, failure);
                    }
                }
                else if (mediaType == NativeGallery.MediaType.Video)
                {
                    Action<ChatMessageAttachementUploaded[]> success = responses =>
                    {
                        string assetUrl;
                        string thumbUrl;
                        switch (responses.Length)
                        {
                            case 1:
                                assetUrl = responses[0].AssetUrl;
                                thumbUrl = null;
                                break;
                            case 2:
                                assetUrl = responses[1].AssetUrl;
                                thumbUrl = responses[0].AssetUrl;
                                break;
                            default:
                                _errorScreen.SetupErrorRaw(_localizationManager.getLocalizedTextWithArgs(SocialErrors.AttachmentFileError, path));
                                _teamChatView.ShowAttachmentLoading(false);
                                return;
                        }

                        _activityRewardsManager.TryRewardAttachmentMessage();

                        var videoProperties = NativeGallery.GetVideoProperties(path);
                        var width = videoProperties.width;
                        var height = videoProperties.height;

                        // if rotation is not any of landscape, we need to flip dimensions
                        if (Mathf.RoundToInt(videoProperties.rotation) / 90 % 2 == 1)
                        {
                            width = videoProperties.height;
                            height = videoProperties.width;
                        }

                        _chatManager.SendMessage(new ChatMessageRequest()
                        {
                            MessageType = MessageType.Attachment.ToString(),
                            Text = "",
                            Attachments = new List<ChatMessageAttachementRequest>()
                            {
                                new()
                                {
                                    AssetUrl = assetUrl,
                                    ThumbUrl = thumbUrl,
                                    Type = "video",
                                    OriginalHeight = height > 0 ? height : null,
                                    OriginalWidth = width > 0 ? width : null,
                                    AdditionalProperties = new Dictionary<string, object>()
                                    {
                                        { ChatMessageAdditionalProperties.VideoOrientation, Mathf.RoundToInt(videoProperties.rotation) },
                                    },
                                }
                            }
                        }, () => { _teamChatView.ShowAttachmentLoading(false); });
                    };
                    Action failure = () =>
                    {
                        _errorScreen.SetupError(SocialErrors.TeamsImageUploadError);
                        _teamChatView.ShowAttachmentLoading(false);
                        return;
                    };
                    _chatManager.UploadVideoToChat(path, success, failure);
                }
            }
            else
            {
                _teamChatView.ShowAttachmentLoading(false);
            }
        }

        private void CreateTeamRequestHandler(TeamCreationData teamCreationData)
        {
            if (TryHandleConnectionProblem()) return;

            var transaction = new Transaction().Spend(_socialManager.CreateTeamPrice);
            if (!_walletManager.TransactionController.CanMakeTransaction(transaction))
            {
                var storeModalController = _modalsBuilder.CreateModalView<StoreModalController>(ModalsType.Store);
                storeModalController.SetupOnCloseAction(() =>
                {
                    _socialManager.ShowSocialModal();
                    _browseTeamView.SelectTab(BrowseTeamView.NoTeamTabs.Create, true);
                }, true);

                PurchasePath.Start(PurchaseStep.CreateTeam);
                storeModalController.Show(StoreCategory.Regular);
                _socialManager.HideSocialModal();
                return;
            }

            _loaderHolder.SetActive(true);
            _socialManager.CreateTeam(teamCreationData, (success, error) =>
            {
                if (success)
                {
                    var transaction = new Transaction()
                        .Spend(_socialManager.CreateTeamPrice)
                        .SetAnalyticsData(CurrencyFlow.Social.Name, CurrencyFlow.Social.CreateTeam, string.Empty)
                        .AddTag(TransactionTag.Teams);

                    _walletManager.TransactionController.MakeTransaction(transaction);
                    _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.Teams);

                    SubscribeChatConnect();
                }

                if (!success)
                {
                    _errorScreen.SetupError(error);
                }
            });
        }

        private void CloseTeamInfoHandler()
        {
            TryHandleConnectionProblem(true);
        }

        private void EditTeamButtonHandler(TeamPublicInfo teamPublicInfo)
        {
            if (TryHandleConnectionProblem()) return;

            _teamEditScreen.Show();
            _teamEditScreen.Setup(teamPublicInfo, () => { _teamEditScreen.Hide(); }, buttonTransform =>
            {
                if (TryHandleConnectionProblem(target: buttonTransform)) return;

                var editedTeamData = _teamEditScreen.TeamDataView;
                _loaderHolder.SetActive(true);
                _socialManager.EditTeam(new TeamCreationData()
                {
                    Name = editedTeamData.TeamName,
                    Description = editedTeamData.TeamDescription,
                    Icon = editedTeamData.LogoUid,
                    RequiredLevel = editedTeamData.RequiredLevel,
                    TeamType = editedTeamData.TeamType,
                }, (success, error) =>
                {
                    _loaderHolder.SetActive(false);
                    _teamChatView.SetupCurrentTeam(_socialManager.CurrentTeam);

                    if (!success)
                        _errorScreen.SetupError(error);
                });

                _teamEditScreen.Hide();
            });
        }

        private void ConnectedToChatHandler()
        {
            UnsubscribeChatConnect();
            _loaderHolder.SetActive(false);
            RefreshTeamState();
        }

        private bool TryHandleConnectionProblem(bool showView = false, Transform target = null)
        {
            if (ConnectivityStatusManager.ConnectivityReachable) return false;

            if (showView)
            {
                ShowNoConnectionView();
                return true;
            }

            target ??= _floatingTextAnchor;
            _overlayDialogConfig.DisplayType = DisplayType.FloatingText;
            _overlayDialogConfig.TargetTransform = target;
            _overlayDialogConfig.ShowBackground = true;
            _overlayDialogConfig.TextToDisplay = LocalizationManagerHelper.OfflineConnectionProblemKey;
            _overlayDialogManager.ShowOverlayDialog(_overlayDialogConfig);
            return true;
        }

        private void JoinTeamButtonHandler(TeamPublicInfo teamPublicInfo)
        {
            if (TryHandleConnectionProblem()) return;

            _loaderHolder.SetActive(true);
            _socialManager.JoinTeam(teamPublicInfo.Id, (success, error) =>
            {
                if (!success)
                {
                    _errorScreen.SetupError(error);
                    _loaderHolder.SetActive(false);
                }
                else
                    SubscribeChatConnect();
            });
        }

        private void ResignButtonHandler(TeamPublicInfo teamPublicInfo)
        {
            if (TryHandleConnectionProblem()) return;

            _dialogScreen.Setup(ResignTitle, ResignDescription, ResignConfirm, ResignDecline, () =>
            {
                _socialManager.ResignTeam(teamPublicInfo.Id, (success, error) =>
                {
                    if (!success)
                    {
                        _errorScreen.SetupError(error);
                        _loaderHolder.SetActive(false);
                    }
                });
                
                _teamInfoView.HideLeaderButtons();
                _teamInfoView.Hide();
                _dialogScreen.Hide();
            }, () => { _dialogScreen.Hide(); });

            _dialogScreen.Show();
        }

        private void LeaveTeamButtonHandler(TeamPublicInfo teamPublicInfo)
        {
            if (TryHandleConnectionProblem()) return;

            _dialogScreen.Setup(TeamsLeaveTitle, TeamsLeaveDescription, TeamsLeaveConfirm, TeamsLeaveDecline, () =>
            {
                _dialogScreen.Hide();

                if (TryHandleConnectionProblem()) return;

                _loaderHolder.SetActive(true);
                _socialManager.LeaveTeam(teamPublicInfo.Id, (success, error) =>
                {
                    _loaderHolder.SetActive(false);
                    _browseTeamView.RemoveTeamFromCachedList(teamPublicInfo.Id);
                    RefreshTeamState();

                    if (!success)
                        _errorScreen.SetupError(error);
                });
            }, () => { _dialogScreen.Hide(); });

            _dialogScreen.Show();
        }

        private void AdminHandler(TeamMemberInfo teamMemberInfo)
        {
            if (TryHandleConnectionProblem()) return;

            if (teamMemberInfo.IsLeader)
            {
                _dialogScreen.SetupRawText(_localizationManager.getLocalizedText(AdminDialog2Title), _localizationManager.getLocalizedTextWithArgs(AdminDialog2Description, teamMemberInfo.Name),
                    _localizationManager.getLocalizedText(AdminDialog2Yes), _localizationManager.getLocalizedText(AdminDialog2No), () =>
                    {
                        _dialogScreen.Hide();

                        if (TryHandleConnectionProblem()) return;

                        _loaderHolder.SetActive(true);
                        _socialManager.AdminPlayer(_socialManager.CurrentTeam.TeamUid, teamMemberInfo, (success, error) =>
                        {
                            _loaderHolder.SetActive(false);

                            if (!success)
                                _errorScreen.SetupError(error);
                        });
                    }, () => { _dialogScreen.Hide(); });
            }
            else
            {
                _dialogScreen.SetupRawText(_localizationManager.getLocalizedText(AdminDialog1Title), _localizationManager.getLocalizedTextWithArgs(AdminDialog1Description, teamMemberInfo.Name),
                    _localizationManager.getLocalizedText(AdminDialog1Yes), _localizationManager.getLocalizedText(AdminDialog1No), () =>
                    {
                        _dialogScreen.Hide();

                        if (TryHandleConnectionProblem()) return;

                        _loaderHolder.SetActive(true);
                        _socialManager.AdminPlayer(_socialManager.CurrentTeam.TeamUid, teamMemberInfo, (success, error) =>
                        {
                            _loaderHolder.SetActive(false);

                            if (!success)
                                _errorScreen.SetupError(error);
                        });
                    }, () => { _dialogScreen.Hide(); });
            }

            _dialogScreen.Show();
        }

        private void KickHandler(TeamMemberInfo teamMemberInfo, bool banUser)
        {
            if (TryHandleConnectionProblem()) return;

            // Show dialog 1
            var dialog1Title = banUser ? KickBanDialog1Title : KickDialog1Title;
            var dialog1Description = banUser ? KickBanDialog1Description : KickDialog1Description;

            _dialogScreen.SetupRawText(
                _localizationManager.getLocalizedText(dialog1Title),
                _localizationManager.getLocalizedTextWithArgs(dialog1Description, teamMemberInfo.Name),
                _localizationManager.getLocalizedText(KickDialog1Yes),
                _localizationManager.getLocalizedText(KickDialog1No),
                OnDialog1Confirmed,
                HideDialog);

            _dialogScreen.Show();

            return;

            // Local function to handle dialog 1 confirmation
            void OnDialog1Confirmed()
            {
                if (TryHandleConnectionProblem())
                {
                    _dialogScreen.Hide();
                    return;
                }

                if (banUser)
                {
                    ShowDialog2();
                }
                else
                {
                    ExecuteKick();
                }
            }

            void HideDialog()
            {
                _dialogScreen.Hide();
            }

            // Show dialog ban confirmation
            void ShowDialog2()
            {
                if (TryHandleConnectionProblem())
                {
                    _dialogScreen.Hide();
                    return;
                }

                _dialogScreen.SetupRawText(
                    _localizationManager.getLocalizedText(KickDialog2Title),
                    _localizationManager.getLocalizedTextWithArgs(KickDialog2Description, teamMemberInfo.Name),
                    _localizationManager.getLocalizedText(KickDialog2Yes),
                    _localizationManager.getLocalizedText(KickDialog2No),
                    ExecuteKick,
                    HideDialog);
            }

            void ExecuteKick()
            {
                _dialogScreen.Hide();

                if (TryHandleConnectionProblem()) return;

                _loaderHolder.SetActive(true);
                _socialManager.KickPlayer(_socialManager.CurrentTeam.TeamUid, teamMemberInfo, banUser, (success, error) =>
                {
                    _loaderHolder.SetActive(false);

                    if (!success)
                        _errorScreen.SetupError(error);
                });
            }
        }

        private void SendMessageRequestHandler(string messageText, Action onSendingMessageStart)
        {
            if (TryHandleConnectionProblem()) return;

            onSendingMessageStart.SafeInvoke();
            _activityRewardsManager.TryRewardRegularMessage();
            _chatManager.SendMessage(new ChatMessageRequest()
            {
                MessageType = MessageType.Regular.ToString(),
                Text = messageText,
            });
        }

        private void AskForLivesRequestHandler(Action askForLivesCallback)
        {
            if (TryHandleConnectionProblem())
            {
                askForLivesCallback.SafeInvoke();
                _chatManager.HandleNoInternetConnection();
                return;
            }

            _socialManager.AskForLives((success, error) =>
            {
                if (!success)
                    _errorScreen.SetupError(error);

                askForLivesCallback.SafeInvoke();
            });
        }

        private void NudgeTeamEventRequestHandler(Action nudgeTeamEventCallback)
        {
            if (TryHandleConnectionProblem())
            {
                nudgeTeamEventCallback.SafeInvoke();
                _chatManager.HandleNoInternetConnection();
                return;
            }

            _socialManager.NudgeTeamCoop((success, error) =>
            {
                if (!success)
                    _errorScreen.SetupError(error);

                nudgeTeamEventCallback.SafeInvoke();
            });
        }

        private void HelpButtonClickedHandler(ChatMessage message, Action onHelpSuccess, Action<string> onHelpFail)
        {
            if (TryHandleConnectionProblem())
            {
                onHelpFail.SafeInvoke(SEND_HELP_NO_INTERNET_ERROR);
                _chatManager.HandleNoInternetConnection();
                return;
            }

            _socialManager.SendHelp(message, (success, error) =>
            {
                if (success)
                {
                    onHelpSuccess.SafeInvoke();
                }
                else
                {
                    _errorScreen.SetupError(SEND_HELP_SERVER_ERROR);
                    onHelpFail.SafeInvoke(error);
                }
            });
        }

        private void ClaimIapClickedHandler(ChatMessage message, string productUid, Dictionary<string, int> reward, Action onClaimIapSuccess, Action onClaimIapFail)
        {
            if (TryHandleConnectionProblem())
            {
                onClaimIapFail.SafeInvoke();
                _chatManager.HandleNoInternetConnection();
                return;
            }

            _socialManager.ClaimIap(message, productUid, reward, (success, error) =>
            {
                if (success)
                {
                    onClaimIapSuccess.SafeInvoke();
                }
                else
                {
                    onClaimIapFail.SafeInvoke();
                }
            });
        }

        private void UpdateChatConnection(bool initial = false)
        {
            if (!_accountManager.IsInTeam)
            {
                return;
            }

            if (_chatManager.IsChatConnected())
            {
                RefreshTeamState();
            }
            else
            {
                if (initial)
                {
                    _teamChatView.Clear();
                }

                if (ConnectivityStatusManager.ConnectivityReachable)
                {
                    _teamChatView.ShowChatLoading();
                    SubscribeChatUpdateSuccess();
                    _chatManager.UpdateChatConnection();
                }
                else
                {
                    UpdateChatConnectionFailed(true);
                }
            }
        }

        private void UpdateChatConnectionComplete()
        {
            UnsubscribeChatUpdateSuccess();
            RefreshTeamState();
        }

        private void UpdateChatConnectionFailed(bool repeatedFailure)
        {
            UnsubscribeChatUpdateSuccess();
            _teamChatView.RemoveChatLoading();
            _teamChatView.UpdateConnectionState();

            if (_chatConnectionRetryCoroutine != null)
            {
                StopCoroutine(_chatConnectionRetryCoroutine);
            }

            float delay = repeatedFailure ? ChatConnectionRetryDelay : ChatConnectionRetryShortDelay;
            _chatConnectionRetryCoroutine = StartCoroutine(RetryChatConnection(delay));
        }

        private IEnumerator RetryChatConnection(float delay)
        {
            yield return WaitCache.Seconds(delay);
            UpdateChatConnection();
        }

        private void ReactionSelectedHandler(ChatMessage message, string reactionUid, Action onReactionSelectFail)
        {
            if (TryHandleConnectionProblem())
            {
                onReactionSelectFail.SafeInvoke();
                return;
            }

            _chatManager.SendReaction(message, reactionUid);
        }

        private void DeleteMessageHandler(ChatMessage message)
        {
            if (TryHandleConnectionProblem()) return;

            _chatManager.DeleteMessage(message);
        }

        private void FlagMessageHandler(ChatMessage message)
        {
            if (TryHandleConnectionProblem()) return;

            _chatManager.FlagMessage(message);
            _errorScreen.SetupInfoMessage(ErrorScreen.TeamInfoTitle, "TEAM_INFO_SCREEN_REPORT_TEXT");
        }

        private void ChatMessagesAddedHandler(ChatMessage message)
        {
            _teamChatView.AddMessage(message);
        }

        private void ChatMessagesUpdatedHandler(ChatMessage message)
        {
            _teamChatView.UpdateMessage(message);
        }

        private void ChatMessagesDeletedHandler(ChatMessage message)
        {
            _teamChatView.DeleteMessage(message);
        }
    }
}