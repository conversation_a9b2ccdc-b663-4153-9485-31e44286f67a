using System.Collections.Generic;
using BBB.Core.Analytics;
using PBGame;
using BBB;

namespace GameAssets.Scripts.SocialScreens.Teams
{
    public class TeamChangedEvent : BaseEvent
    {
        public TeamChangedEvent(TeamData teamData, TeamDataCacheState prevTeamData, bool isTeamJoinedFromPopup)
        {
            var data = new Dictionary<string, object>();
            if (teamData != null)
            {
                data.Add(BBB.Core.Analytics.Social.Team.TeamUid, teamData.TeamUid);
                data.Add(BBB.Core.Analytics.Social.Team.TeamName, teamData.Name);
                data.Add(BBB.Core.Analytics.Social.Team.TeamSize, teamData.Members?.Count ?? 0);
                data.Add(BBB.Core.Analytics.Social.Team.IsTeamJoinedFromPopup, isTeamJoinedFromPopup);
            }

            if (prevTeamData != null)
            {
                data.Add(BBB.Core.Analytics.Social.Team.PrevTeamUid, prevTeamData.LastTeamUid);
                data.Add(BBB.Core.Analytics.Social.Team.PrevTeamName, prevTeamData.LastTeamName);
                data.Add(BBB.Core.Analytics.Social.Team.PrevTeamSize, prevTeamData.LastTeamSize);
            }

            Initialize(BBB.Core.Analytics.Social.Team.JoinName, data);
        }
    }

    public class TeamExitEvent : BaseEvent
    {
        public TeamExitEvent(TeamData teamData)
        {
            var data = new Dictionary<string, object>();

            if (teamData != null)
            {
                data.Add(BBB.Core.Analytics.Social.Team.TeamUid, teamData.TeamUid);
                data.Add(BBB.Core.Analytics.Social.Team.TeamName, teamData.Name);
            }
            
            Initialize(BBB.Core.Analytics.Social.Team.ExitName, data);
        }
    }
}