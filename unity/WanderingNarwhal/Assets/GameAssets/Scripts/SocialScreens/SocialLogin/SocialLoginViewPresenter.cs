using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System;
using BBB.Audio;
using BBB.Core;
using BBB.DI;
using BBB.Generic.Modal;
using BBB.Screens;
using BebopBee;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.UI.OverlayDialog;
using TMPro;
using UnityEngine.Profiling;
using UnityEngine.UI.Extensions;

namespace BBB.UI
{
    public class SocialLoginViewPresenter : ModalsViewPresenter, ISocialLoginViewPresenter
    {
        [Serializable]
        public class LanguageData
        {
            public Sprite FlagSprite;
            public SystemLanguage Language;

            public LanguageData(Sprite flagSprite, SystemLanguage language)
            {
                FlagSprite = flagSprite;
                Language = language;
            }
        }

        [SerializeField] private List<LanguageData> _languagesData;
        [SerializeField] private Button _bgCloseButton;
        [SerializeField] private Button _changeNameButton;
        [SerializeField] private Button _deleteAccountButton;
        [SerializeField] private Button _resetAccountButton;
        [SerializeField] private GameObject _languageGo;
        [SerializeField] private GameObject _deleteAccountPanel;
        [SerializeField] private Transform _contentTr;
        [SerializeField] private DropDownList _languageDropDown;
        [SerializeField] private Button _helpButton;
        [SerializeField] private Button _communityJoinNowButton;
        [SerializeField] private Button _communityImageButton;
        [SerializeField] private Button _socialFacebookGroupButton;
        [SerializeField] private Button _socialFacebookButton;
        [SerializeField] private Button _socialInstagramButton;
        [SerializeField] private Button _privacyPolicyButton;
        [SerializeField] private TextMeshProUGUI _playerName;
        [SerializeField] private LocalizedTextPro _currentLanguageText;
        [SerializeField] private AsyncAvatar _asyncAvatar;
        [SerializeField] private Button _rateUsButton;
        [SerializeField] private Button _termsServiceButton;
        [SerializeField] private OverlayDialogConfig _overlayDialogConfig;
        [SerializeField] private SettingsBar _settingsBar;
        [SerializeField] private Transform _floatingTextAnchor;
        [SerializeField] private Button _saveProgressButton;

        private Profile _cachedProfile;
        private List<LanguageData> _languages;
        private IAccountManager _accountManager;
        private IModalsBuilder _modalsBuilder;
        private GenericModalFactory _genericModalFactory;
        private IOverlayDialogManager _overlayDialogManager;

        protected override void OnContextInitialized(IContext context)
        {
            base.OnContextInitialized(context);
            _settingsBar.Init(context);
            _accountManager = context.Resolve<IAccountManager>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _genericModalFactory = context.Resolve<GenericModalFactory>();
            _overlayDialogManager = context.Resolve<IOverlayDialogManager>();
        }

        public event Action OnPlatformLogoutClicked = delegate { };
        public event Action OnResetAccount = delegate { };
        public event Action OnHelpClicked = delegate { };
        public event Action OnJoinNowClicked = delegate { };
        public event Action OnSocialFacebookClicked = delegate { };
        public event Action OnSocialInstagramClicked = delegate { };
        public event Action OnSocialFacebookGroupClicked = delegate { };
        public event Action OnPrivacyPolicyClicked = delegate { };
        public event Action OnNextLanguage = delegate { };
        public event Action OnCustomExitButton = delegate { };
        public event Action RestoreIAPClicked = delegate { };
        public event Action OnTermsServiceButton = delegate { };
        public event Action ChangeNameButtonClicked;

        protected override void Awake()
        {
            base.Awake();
            CloseButton.ReplaceOnClick(() =>
            {
                OnCustomExitButton.SafeInvoke();
                PlayCloseFX();
            });

            _helpButton.ReplaceOnClick(() => OnHelpClicked.SafeInvoke());
            _communityJoinNowButton.ReplaceOnClick(() => OnJoinNowClicked.SafeInvoke());
            _communityImageButton.ReplaceOnClick(() => OnJoinNowClicked.SafeInvoke());
            _socialFacebookGroupButton.ReplaceOnClick(() => OnSocialFacebookGroupClicked.SafeInvoke());
            _socialFacebookButton.ReplaceOnClick(() => OnSocialFacebookClicked.SafeInvoke());
            _socialInstagramButton.ReplaceOnClick(() => OnSocialInstagramClicked.SafeInvoke());
            _privacyPolicyButton.ReplaceOnClick(() => OnPrivacyPolicyClicked.SafeInvoke());
            _bgCloseButton.ReplaceOnClick(() =>
            {
                OnCustomExitButton.SafeInvoke();
                AudioProxy.PlaySound(GenericSoundIds.GenericPopupAppearing);
            });
            _rateUsButton.ReplaceOnClick(() => RestoreIAPClicked.SafeInvoke());
            _termsServiceButton.ReplaceOnClick(() => OnTermsServiceButton.SafeInvoke());
            _changeNameButton.ReplaceOnClick(() => ChangeNameButtonClicked.SafeInvoke());
            _saveProgressButton.ReplaceOnClick(SaveProgressButtonClicked);
            _resetAccountButton.ReplaceOnClick(() => OnResetAccount.SafeInvoke());
            
            _deleteAccountButton.ReplaceOnClick(() =>
            {
                AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
                if (!ConnectivityStatusManager.ConnectivityReachable)
                {
                    _genericModalFactory.ShowNoConnectionModal();
                    return;
                }
                
                AudioProxy.PlaySound(GenericSoundIds.GenericPopupAppearing);
                _deleteAccountPanel.SetActive(true);
            });
            _deleteAccountPanel.SetActive(false);
        }

        public void Setup(Profile profile, List<LanguageData> languages)
        {
            _cachedProfile = profile;
            _languages = new List<LanguageData>(languages);
            _languageDropDown.Disable();
        }

        public void UpdatePlatformInfo(bool logged, Profile profile)
        {
            _cachedProfile = profile;
            _playerName.text = _accountManager.Profile.Name;
        }

        public void DisplayFloatingText(string message)
        {
            _overlayDialogConfig.DisplayType = DisplayType.FloatingText;
            _overlayDialogConfig.ShowBackground = false;
            _overlayDialogConfig.TargetTransform = _floatingTextAnchor ?? transform;
            _overlayDialogConfig.TextToDisplay = message;
            _overlayDialogManager.ShowOverlayDialog(_overlayDialogConfig);
        }

        private void SetupAvatar()
        {
            _asyncAvatar.Setup(new AvatarInfo(_cachedProfile.Avatar, _cachedProfile.Country, _cachedProfile.AvatarFrame, _cachedProfile.BadgeUid));
        }

        public void InitViews(Action<SystemLanguage> onSelect, SystemLanguage language)
        {
            if (_contentTr.childCount == 0)
            {
                foreach (var item in _languages)
                {
                    Profiler.BeginSample($"Instantiate[{_languageGo.name}]");
                    var go = Instantiate(_languageGo, _contentTr);
                    Profiler.EndSample();
                    var languageItem = go.GetComponent<LanguageItemView>();
                    var currItem = _languagesData.Find(x => x.Language == item.Language);
                    var sprite = currItem.FlagSprite;
                    languageItem.Init(sprite, item.Language, onSelect);
                }
            }

            SetLanguage(language);
            SetupAvatar();
            _playerName.text = _accountManager.Profile.Name;
        }

        protected override void OnCloseButtonClick()
        {
            BDebug.Log(LogCat.Social, "Getting clicked!!");
        }

        public void SetLanguage(SystemLanguage language)
        {
            _currentLanguageText.SetTextId($"{language}_TRANSLATED");
        }

        private void SaveProgressButtonClicked()
        {
            AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                _genericModalFactory.ShowNoConnectionModal();
                return;
            }

            var ctrl = _modalsBuilder.CreateModalView<SaveProgressModalController>(ModalsType.SaveProgress);
            ctrl.ShowModal();
        }
    }
}