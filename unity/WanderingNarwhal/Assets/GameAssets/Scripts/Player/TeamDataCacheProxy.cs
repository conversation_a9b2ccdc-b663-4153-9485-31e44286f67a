using BBB;
using PBGame;

namespace GameAssets.Scripts.Player
{
    public class TeamDataCacheProxy
    {
        private readonly TeamDataCacheState _teamDataCache;

        public TeamDataCacheProxy(TeamDataCacheState teamDataCache)
        {
            _teamDataCache = teamDataCache;
        }

        public void UpdatePrevTeamData(TeamData teamData)
        {
            _teamDataCache.LastTeamUid = teamData.TeamUid;
            _teamDataCache.LastTeamName = teamData.Name;
            _teamDataCache.LastTeamSize = teamData.Members?.Count ?? 0;
        }

        public TeamDataCacheState GetPrevTeamData()
        {
            if (_teamDataCache.LastTeamUid.IsNullOrEmpty())
            {
                return null;
            }
            
            return _teamDataCache;
        }
    }
}