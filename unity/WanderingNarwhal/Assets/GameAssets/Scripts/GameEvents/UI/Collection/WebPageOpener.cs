using BBB.DI;
using BBB.UI.Core;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    [RequireComponent(typeof(Button))]
    public class WebPageOpener : ContextedUiBehaviour
    {
        [SerializeField] private string _url;
        private Button _button;

        protected virtual string URL => _url;
        
        private void Start()
        {
            _button = GetComponent<Button>();
            _button.onClick.AddListener(OpenWebPage);
        }

        private void OpenWebPage()
        {
            Application.OpenURL(URL);
        }

        protected override void InitWithContextInternal(IContext context)
        {
        }
    }
}