using System;
using System.Collections;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using BBB.BrainCloud;
using BBB.Core;
using BBB.DI;
using BBB.GameAssets.Scripts.Player;
using BBB.RaceEvents;
using BBB.Tools;
using BBB.UI.Level;
using BebopBee;
using Beebopbee.Core.Extensions;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.GameEvents;
using GameAssets.Scripts.Player;
using GameAssets.Scripts.Utils;
using PBConfig;
using PBGame;
using UnityEngine;

namespace BBB
{
    /// <summary>
    /// This class represents a GameEvent and is always stored in the memory for both currently active and not active game events
    /// </summary>
    public abstract class GameEventBase : IComparableEvent, IGameEventWithResources, INotifiableEvents
    {
        private const float TimeMissedDifference = 0.1f;
        
        public static readonly Regex BotMask = new(".{6}b.-.{2}b.-.b.-.b.-.{10}b.");
        
        protected readonly GameEventConfig EventConfig;
        //this state is gonna be null when game event is not active or cleaned up after completion
        protected PBGameEventState State;
        //proxy to the PBPlayer data
        protected GameEventStateProxy StateProxy => PlayerManager.Player.GameEventStateProxy;
        protected readonly IConfig Config;
        protected readonly IPlayerManager PlayerManager;
        protected readonly ILocalizationManager LocalizationManager;
        //interfaces for access to the outside data and notify outside global systems about the crucial change in the evnet
        protected readonly IGlobalGameEventDataProvider GlobalDataProvider;
        protected readonly IGlobalGameEventChangesNotifier ChangeNotifier;

        //Can be Absolute or Relative
        //Absolute scheduling means that event starts on the absolute date specified in the config
        //Relative means that all Relative events start on RelativeTimelineStart and then cycle one after another
        private readonly SchedulingType _schedulingType;
        private readonly List<List<DayOfWeek>> _recurringWeekdayPeriods;
        protected readonly BaseNotifier Notifier;
        private readonly ICoroutineExecutor _coroutineExecutor;
        private Coroutine _liveOpsRoutine;
        private readonly int _liveOpsAnalyticsInterval;
        private IEventDispatcher _eventDispatcher;

        public int LastAddedScoreToShow { get; protected set; }
        public virtual bool IsUsedInUnifiedWidget => false;
        public string EventResourceId => EventConfig.Uid.RemoveDigits();
        public abstract GameEventGameplayType GameplayType { get; }
        public virtual bool ShouldGoToNextLevelFromInfoModal => false;
        // find better name
        public virtual bool ShowCurrencyOnLevelSuccessPlayNextButton => false;
        public virtual bool ShowExplore => true;
        public virtual bool CanCloseInfoModal => true;
        public virtual bool ShowVictoryModal => true;
        public bool ShouldBeReleased { get; private set; }
        public virtual bool ShouldProcessForLaunch => false;

        public bool ShouldKeepResourceLoaded()
        {
            return Status.ShouldKeepResourceLoaded();
        }

        public string NameText => LocalizationManager.getLocalizedText(EventConfig.DisplayName);

        public string DescriptionText => LocalizationManager.getLocalizedText(EventConfig.DisplayDescription);

        public string DetailsText => LocalizationManager.getLocalizedText(EventConfig.DisplayDetails);

        public string VictoryMessageText => LocalizationManager.getLocalizedText(EventConfig.VictoryMessage);

        public string TripstagramInvitationMessageText => LocalizationManager.getLocalizedText(EventConfig.TripstagramInvitationMessage);

        public string FailureMessageText => LocalizationManager.getLocalizedText(EventConfig.FailureMessage);

        public string ShortNameText => LocalizationManager.getLocalizedText(EventConfig.ShortName);

        public abstract float ProgressRatio { get; }
        public abstract float ProgressRatioLastDelta { get; }

        public int ScoreGoal => EventConfig.EventScoreGoal;

        //here we cache last added delta to visualize it in UI progress bars
        public int LastAddedScoreDelta => State?.LastAddedScoreDelta ?? 0;
        public int PreviousTotalScore => (State?.CurrentScore ?? 0) - (State?.LastAddedScoreDelta ?? 0);

        //value for current score progress
        public int CurrentScore => State?.CurrentScore ?? 0;

        protected GameEventStatus StatusInternal
        {
            get
            {
                if (State == null)
                    return GameEventStatus.Missing;

                return (GameEventStatus)State.Status;
            }
        }

        public GameEventStatus Status => StatusInternal;

        public virtual (string, string) CurrentNarrativeIds => (null, null);

        protected float TimeProgress
        {
            get
            {
                var timeLeftSeconds = (float)GetTimeLeft().TotalSeconds;
                var durationSeconds = EventConfig.DurationInDays * 24 * 60 * 60;
                return (durationSeconds - timeLeftSeconds) / durationSeconds;
            }
        }
        
        public void SetLastScoreSeenFor(string uid, int score)
        {
            if (State != null)
            {
                State.LastShownScore ??= new Dictionary<string, int>();
                State.LastShownScore[uid] = score;
            }
        }

        public int GetLastScoreSeenFor(string uid)
        {
            if (State?.LastShownScore == null)
                return 0;

            return State.LastShownScore.TryGetValue(uid, out var value) ? value : 0;
        }
        
        protected virtual bool ShouldReleaseOnExpire => true;

        public bool WaitingToBeNotified => State != null && State.WaittingToBeNotified;

        //array of two values representing range for random generation of scores possible to collect on a certain level
        protected int[] MinMaxScore => EventConfig.MinMaxScoreForLevel;

        public string Uid => EventConfig.Uid;

        public SchedulingType SchedulingType => _schedulingType;

        /// <summary>
        /// Total duration of the event in days, same config value is used for both Absolute and Relative scheduling
        /// </summary>
        public int Duration => EventConfig.DurationInDays;

        /// <summary>
        /// For Relative scheduling we specify the sort index to know which event should go first or next
        /// </summary>
        public int RelativeSortIndex => EventConfig.RelativeSortIndex;

        public string CharacterSpriteName => EventConfig.CharacterSpriteName;
        
        public string BundlesLoadLevelUid => EventConfig.BundlesLoadLevelUid;

        /// <summary>
        /// Determines if the event should be autoshown on the next map opening
        /// </summary>
        public virtual bool ShouldQualificationAnnouncementBeAutoShown => ShouldBeAutoShown(() => State.WaitingForShowcase);
        public bool ShouldStartAnnouncementBeAutoShown => ShouldBeAutoShown(ShouldShowIntroduction);
        public virtual bool ShouldEndAnnouncementBeAutoShown => ShouldBeAutoShown(WaitingToShowResult);

        private bool ShouldBeAutoShown(Func<bool> autoShownCondition)
        {
            if (ShouldNeverAutoShow() || State == null)
                return false;

            return IsDebugModeAndEventLaunched() || autoShownCondition();
        }

        private bool IsDebugModeAndEventLaunched()
        {
            var status = (GameEventStatus)State.Status;
            return GlobalDataProvider.IsInDebugMode && status.IsLaunched();
        }

        public virtual bool HasNoResources()
        {
            return false;
        }

        public virtual bool ShouldNeverShowHudIcon()
        {
            return false;
        }

        protected virtual bool ShouldNeverAutoShow()
        {
            return false;
        }

        protected virtual bool IsQualified() => false;
        
        protected virtual bool ShouldShowIntroduction() => false;

        public abstract bool WaitingToShowResult();
        
        public abstract bool IsReadyToCollect();

        public string DisplayName => EventConfig == null ? "" : EventConfig.DisplayName;

        protected IAccountManager AccountManager { get; }

        protected GameEventBase(GameEventConfig eventConfig, IContext context, IGlobalGameEventDataProvider dataProvider, IGlobalGameEventChangesNotifier changeNotifier)
        {
            ChangeNotifier = changeNotifier;
            EventConfig = eventConfig;
            GlobalDataProvider = dataProvider;
            AccountManager = context.Resolve<IAccountManager>();
#if UNITY_EDITOR || BBB_DEBUG
            DebugApplyOverrideConfig();
#endif

            Config = context.Resolve<IConfig>();
            PlayerManager = context.Resolve<IPlayerManager>();
            LocalizationManager = context.Resolve<ILocalizationManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();

            _schedulingType = eventConfig.SchedulingType.TryParseToEnum<SchedulingType>();

            State = StateProxy?.GetGameEventState(EventConfig.Uid);

            Notifier = new BaseNotifier();

            if (!string.IsNullOrWhiteSpace(eventConfig.WeekdaysSchedule))
            {
                var activeWeekDays = new List<DayOfWeek>();
                var activeWeekDaysStr = eventConfig.WeekdaysSchedule.Split(',');
                foreach (var weekdayStr in activeWeekDaysStr)
                {
                    var weekDayEnum = weekdayStr.TryParseToEnum<DayOfWeek>(true);

                    activeWeekDays.Add(weekDayEnum);
                }

                if (_schedulingType == SchedulingType.Weekday && EventConfig.DurationInDays != activeWeekDays.Count)
                {
                    BDebug.LogError(LogCat.General, 
                        $"Duration in days {EventConfig.DurationInDays} does not fit " +
                        $"number of days in {eventConfig.WeekdaysSchedule}");
                }
            }
            _coroutineExecutor = context.Resolve<ICoroutineExecutor>();
            
            _liveOpsAnalyticsInterval = GameEventUtils.GetLiveOpsAnalyticsInterval(Config, Uid);
            if (_liveOpsAnalyticsInterval > 0)
            {
                _liveOpsRoutine = _coroutineExecutor.StartCoroutine(TriggerLiveOpsAnalyticsSnapshot());
            }
            
            #if UNITY_EDITOR
            EditorInstanceTracker.AddInstance(this);
            #endif
        }

        ~GameEventBase()
        {
            #if UNITY_EDITOR
            EditorInstanceTracker.RemoveInstance(this);
            #endif
        }

#if UNITY_EDITOR || BBB_DEBUG
        /// <summary>
        /// Override config is customizeable from Exra Debug menu. It allows to force specific game event or turn off any active event. 
        /// </summary>
        private void DebugApplyOverrideConfig()
        {
            var overrideEventUidSubstring = GlobalDataProvider.GetOverrideGameEvent();
            if (overrideEventUidSubstring.IsNullOrEmpty()) return;

            if (EventConfig.Uid.Contains(overrideEventUidSubstring))
            {
                EventConfig.DurationInDays = 10;
                EventConfig.AbsoluteStartTime = new DayMonthYear()
                {
                    Year = 2020,
                    Month = DateTime.Now.Month,
                    Day = Mathf.Max(1, DateTime.Now.Day - 1),
                };
            }
            else
            {
                EventConfig.DurationInDays = 1;
                EventConfig.AbsoluteStartTime = new DayMonthYear()
                {
                    Year = 2010,
                    Month = 1,
                    Day = 1,
                };
            }

            EventConfig.SchedulingType = SchedulingType.Absolute.ToString();
        }
#endif

        /// <summary>
        /// Final narrative is the narrative shown after winning or failing the game event
        /// </summary>
        /// <param name="victory">This args defines if it is the win or lose</param>
        /// <returns></returns>
        public abstract string GetCurrentRewardFollowingNarrativeId();
        public abstract GameEventRewardLocParams GetCurrentRewardLocalizationParams();

        public abstract void AddScore(int delta, bool append = false);

        public virtual void DebugSetScore(int score) {}

        protected abstract bool IsReadyToRelease();

        public bool ActiveTimeNow()
        {
            var utcNow = GlobalDataProvider.GetCurrentUtcDateTime();
            return DoesMyTimeSpanContain(utcNow);
        }

        private bool IsEventStatusValid()
        {
            var status = StatusInternal;
            if (Enum.IsDefined(typeof(GameEventStatus), status)) return true;

            BDebug.LogWarning(LogCat.General, $"Game event {Uid} has invalid status: {status}.");
            return false;
        }

        /// <summary>
        /// This method is called every time we open the city map but before resource reload
        /// It is responsible for game event status changes such as making some event active
        /// or registering failure if the time passed
        /// as well as releasing the event if reward is collected and time passed
        /// </summary>
        public virtual void TryProcessState()
        {
            var utcNow = GlobalDataProvider.GetCurrentUtcDateTime();
            var currentStatus = StatusInternal;

            //If reward is collected and time passed we release the event
            if (IsReadyToRelease() || !IsEventStatusValid())
            {
                Release();
            }
            //if event is missing the state or in intial status and both level and time condition is satisfied
            //we lauch the event
            else if (currentStatus.IsReadyToLaunch() && ConnectivityStatusManager.ConnectivityReachable)
            {
                if (AvailableForHighestPassedLevel() && DoesCurrentTimeSpanContain(utcNow))
                {
                    Launch(currentStatus);
                }
            }
            //if the event is active but time already passed we register failure and ask to show it 
            else if (currentStatus == GameEventStatus.Active && (!DoesMyTimeSpanContain(utcNow) || DoesTimeWindowMissed()))
            {
                EventExpired();
            }

            if (State == null) return;
            
            if (State.WaittingToBeNotified)
                Notifier.SetNotifier(1);
            
            ScoreUpdated();
        }
        
        protected virtual bool AvailableForHighestPassedLevel()
        {
            var requiredLevelUid = EventConfig?.RequiredLevelUid;
            var highestPassedLevelId = AccountManager?.Profile?.HighestPassedLevelId;
            
            if(requiredLevelUid.IsNullOrEmpty() || highestPassedLevelId.IsNullOrEmpty()) return false;
            
            var requiredLevelSortOrder = LevelHelper.GetLevelSortOrder(Config, requiredLevelUid);
            var highestPassedSortOrder = LevelHelper.GetLevelSortOrder(Config, highestPassedLevelId);
            return highestPassedSortOrder >= requiredLevelSortOrder;
        }

        protected virtual bool DoesTimeWindowMissed()
        {
            if (State is not { EventLaunchUnixUtcTime: > 0.0 }) return false;
            
            var currentUnixStartTime = GetCurrentStartTime().ToUnixTimeSeconds();
            var launchUnixUtcTime = State.EventLaunchUnixUtcTime;

            return Math.Abs(currentUnixStartTime - launchUnixUtcTime) > TimeMissedDifference;
        }

        private void EventExpired()
        {
            State.Status = (int)GetExpirationStatus();
            HandleExpiration();
                        
            if (!IsReadyToRelease()) return;
            
            ShouldBeReleased = true;
            if (ShouldReleaseOnExpire)
            {
                Release();
            }
        }

        protected abstract GameEventStatus GetExpirationStatus();
        protected abstract void HandleExpiration();

        //this method is launching the event and transitioning it to active status as well as creates the state object for it if needed
        private void Launch(GameEventStatus currentStatus)
        {
            // First try to get existing state, even if status is Missing (to handle race conditions)
            var newState = StateProxy.GetGameEventState(EventConfig.Uid);
            
            // If no existing state and status is Missing, create a new one
            if (newState == null && currentStatus == GameEventStatus.Missing)
            {
                newState = StateProxy.CreateGameEventState(EventConfig.Uid);
            }

            if (newState == null)
            {
                BDebug.LogError(LogCat.General, $"Fail to launch {EventConfig.Uid}");
                return;
            }

            var status = (GameEventStatus)newState.Status;

            if (status == GameEventStatus.None)
            {
                newState.LocalLeaderboardVersion = -1;
                newState.Status = (int)GameEventStatus.Active;
                newState.WaitingForShowcase = true;
                newState.JustStarted = true;
                State = newState;
                SetScore(0);

                var startTime = GetCurrentStartTime();
                var endTime = GetCurrentEndTime();
                newState.EventLaunchUnixUtcTime = startTime.ToUnixTimeSeconds();
                newState.EventEndUnixUtcTime = endTime.ToUnixTimeSeconds();
                newState.FailedAttempts = 0;
                newState.MultiplierStreak = 0;
                newState.MultiplierStreakCached = 0;
                
                HandleLaunch();

                PlayerManager.MarkDirty();
                ChangeNotifier.OnLaunch(this);
            }
            else
            {
                // Event already launched by another thread, just update our state reference
                State = newState;
                // Don't log error - this is expected in race conditions
            }
        }
        
        public virtual void SetScore(int score)
        {
            State.CurrentScore = score;
            ScoreUpdated();
        }

        private void ScoreUpdated()
        {
            var gameEventScoreUpdatedEvent = _eventDispatcher.GetMessage<GameEventScoreUpdatedEvent>();
            gameEventScoreUpdatedEvent.Set(this, State.CurrentScore);
            _eventDispatcher.TriggerEvent(gameEventScoreUpdatedEvent);
        }

        protected abstract void HandleLaunch();
        public abstract Dictionary<string, int> ClaimReward();

        //this method is releasing the state object of the event making its status "Missing"
        protected virtual void Release()
        {
            StopCoroutine();
            StateProxy.ReleaseGameEventState(EventConfig.Uid);
            State = null;
            ShouldBeReleased = false;
            ChangeNotifier.OnRelease(this);
        }

        public void DebugForceRelease()
        {
            Release();
        }


        /// <summary>
        /// Cleans up some data when the popup is close (i.e. if we close the popup then we mark this event not to be
        /// autoshown next time and clean last added score delta  
        /// </summary>
        public virtual void MarkClean()
        {
            if (State == null)
            {
                BDebug.LogError(LogCat.General, $"State {EventConfig.Uid} is not supposed to have null state");
                return;
            }

            State.WaitingForShowcase = false;
            State.WaittingToBeNotified = false;
            State.JustStarted = false;
            State.LastAddedScoreDelta = 0;
            
            Notifier.ResetNotifier();
        }

        public virtual DateTime GetCurrentStartTime()
        {
            return SchedulableConfigUtils.GetLastStartTime(EventConfig, GlobalDataProvider);
        }

        public virtual DateTime GetCurrentEndTime()
        {
            return SchedulableConfigUtils.GetEndTime(EventConfig, GlobalDataProvider);
        }

        public virtual DateTime GetLastStartTime()
        {
            return GetCurrentStartTime();
        }

        public virtual DateTime GetLastEndTime()
        {
            return GetCurrentEndTime();
        }

        public virtual DateTime GetNextStartTime()
        {
            return SchedulableConfigUtils.GetNextStartTime(EventConfig, GlobalDataProvider);
        }

        public virtual DateTime GetNextEndTime()
        {
            return SchedulableConfigUtils.GetNextEndTime(EventConfig, GlobalDataProvider);
        }

        //time left till the end of the event
        public virtual TimeSpan GetTimeLeft(string arg = null)
        {
            return SchedulableConfigUtils.GetTimeLeft(EventConfig, GlobalDataProvider, arg);
        }

        public TimeSpan GetTimeUntilLaunch()
        {
            var currentUtcDateTime = GlobalDataProvider.GetCurrentUtcDateTime();
            var result = GetCurrentStartTime() - currentUtcDateTime;
            if (result.TotalMilliseconds >= 0)
            {
                return result;
            }

            return default;
        }

        //this method checks if the utc date belongs to the event time span (range between start and end time of the event
        protected virtual bool DoesCurrentTimeSpanContain(DateTime currentUtcDateTime)
        {
            return SchedulableConfigUtils.DoesMyTimeSpanContain(EventConfig, GlobalDataProvider, currentUtcDateTime);
        }
        
        protected virtual bool DoesMyTimeSpanContain(DateTime currentUtcDateTime)
        {
            return DoesCurrentTimeSpanContain(currentUtcDateTime);
        }

        public abstract int GenerateScore(Stage stage);

        public abstract bool IsWon();

        public virtual Dictionary<string,object> GetDialogueSpecialParams()
        {
            return null;
        }

        public abstract (string, string) GetDialogueHeader();

        public abstract bool CanShowInScreen(ScreenType screenType);

        public bool ShouldShowHudIcon()
        {
            if (ShouldNeverShowHudIcon())
                return false;

            var status = StatusInternal;
            var utcNow = GlobalDataProvider.GetCurrentUtcDateTime();

            return status is GameEventStatus.Active or GameEventStatus.Accomplished 
                       or GameEventStatus.Ended or GameEventStatus.AccomplishedTripstagramViewed 
                   || (status == GameEventStatus.AccomplishmentRewardCollected && DoesMyTimeSpanContain(utcNow));
        }

        public abstract string GetStartNotificationTitle();
        public abstract string GetStartNotificationMessage();

        public virtual void UpdateRemoteDataIfNeeded()
        {
        }

        public virtual int GetGoalToNextMilestone()
        {
            return 0;
        }

        public virtual bool ExcludeFromLookup()
        {
            return false;
        }

        public INotifierStatus GetNotifier()
        {
            return Notifier;
        }

        public virtual string GetScoreIconSpriteNameForCurrentMilestone() => GameEventResKeys.ScoreIconOutline;


        public bool DidScoreJustChanged()
        {
            return LastAddedScoreToShow > 0;
        }

        public void ClearLastAddedScoreToShow()
        {
            LastAddedScoreToShow = 0;
        }

        public virtual GameEventCurrencyFlowData GetCurrencyFlowAnalyticsData()
        {
            BDebug.LogError(LogCat.General, "GetCurrencyFlowAnalyticsData not implemented for " + GetType().Name);
            return default;
        }

        public virtual Dictionary<string, int> GetWinReward()
        {
            return null;
        }

        public void DecrementFailedAttempt(bool isShuffleFailed = false)
        {
            if (State != null)
            {
                State.FailedAttempts--;
                if (State.FailedAttempts < 0)
                {
                    State.FailedAttempts = 0;
                    BDebug.LogWarning(LogCat.Events, "Failed attempts shouldn't be a negative number. Check Increment and Decrement logic");
                }

                State.MultiplierStreak =
                    isShuffleFailed ? State.MultiplierStreakCached : ++State.MultiplierStreakCached;
            }
        }

        /// <summary>
        /// Increment on the first move and decrement on win to catch the killing app cases
        /// </summary>
        public void IncrementFailedAttempt()
        {
            if (State != null)
            {
                State.FailedAttempts++;
                
                State.MultiplierStreakCached = State.MultiplierStreak;
                State.MultiplierStreak = 0;
            }
        }
        
        public bool IsMultiplierScoreStreakActive()
        {
            if (State == null)
                return false;
            if (EventConfig is not {IsDoubleScoreStreakEnabled: true}) return false;
            var multipliers = EventConfig?.ScoreMultiplier;
            return multipliers is {Count: > 0};
        }
        
        public int GetScoreMultiplier(bool includeCurrentAttempt)
        {
            return State == null ? 1 : GameEventUtils.GetScoreMultiplier(includeCurrentAttempt ? State.MultiplierStreakCached : State.MultiplierStreak, EventConfig?.ScoreMultiplier);
        }
        
        public int GetLowestMultiplier()
        {
            return State == null ? 1 : GameEventUtils.GetLowestMultiplier(EventConfig?.ScoreMultiplier);
        }

        public int GetPreviousScoreMultiplier()
        {
            return State == null ? 1 : GameEventUtils.GetScoreMultiplier(State.MultiplierStreak - 1, EventConfig?.ScoreMultiplier);
        }
        
        public IDictionary<int, int> GetScoreMultipliersForEvent()
        {
            return State != null ? EventConfig?.ScoreMultiplier : null;
        }

        public virtual void UpdateRemoteData(BCUserGameEventData gameDto)
        {
            
        }

        public int CompareTo(IComparableEvent otherEvent)
        {
            var otherGameEvent = otherEvent as GameEventBase;
            if (otherGameEvent == null)
            {
                return -1;
            }
            
            var statusResult = otherGameEvent.Status.CompareTo(Status);
            if (statusResult != 0)
                return statusResult;
                
            return GetTimeLeft().CompareTo(otherGameEvent.GetTimeLeft());
        }

        public virtual void Reset() { }

        public virtual void MigrateData(GameEventBase other)
        {
            LastAddedScoreToShow = other.LastAddedScoreToShow;
            ShouldBeReleased = other.ShouldBeReleased;
        }
        
        private IEnumerator TriggerLiveOpsAnalyticsSnapshot()
        {
            while (State != null)
            {
                yield return WaitCache.Seconds(_liveOpsAnalyticsInterval);
                TriggerLiveOpsEvent(LiveOpsAnalytics.AnalyticNames.SnapShot);
            }
        }

        public virtual void TriggerLiveOpsEvent(string category)
        {
            
        }
        
        public void StopCoroutine()
        {
            if (_liveOpsRoutine != null)
            {
                _coroutineExecutor?.StopCoroutine(_liveOpsRoutine);
                _liveOpsRoutine = null;
            }
        }
        
    }
}