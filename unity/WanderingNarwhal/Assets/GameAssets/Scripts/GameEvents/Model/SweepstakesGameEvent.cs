using BBB.BrainCloud;
using BBB.DI;
using FBConfig;
using PBConfig;
using UnityEngine;

namespace BBB
{
    public class SweepstakesGameEvent : CollectionGameEvent
    {
        private readonly BrainCloudManager _brainCloudManager;
        
        public SweepstakesGameEvent(GameEventConfig eventConfig, IContext context, IGlobalGameEventDataProvider dataProvider, IGlobalGameEventChangesNotifier changeNotifier) : base(eventConfig, context, dataProvider, changeNotifier)
        {
            _milestones.Clear();
            _totalMilestoneScores = 0;
            
            _brainCloudManager = context.Resolve<BrainCloudManager>();

            if (eventConfig.DailyMilestoneConfigs == null)
                return;

            var nextGoal = string.Empty;
            var previousGoal = 0;
            foreach (var dailyMilestoneConfig in eventConfig.DailyMilestoneConfigs)
            {
                var milestone = new SweepstakesMilestone(eventConfig.Uid, dailyMilestoneConfig, previousGoal, nextGoal);
                _milestones.Add(milestone);

                nextGoal = milestone.GoalName;
                _totalMilestoneScores = Mathf.Max(_totalMilestoneScores, milestone.Goal);
                previousGoal = milestone.Goal;
            }
        }

        public override GameEventGameplayType GameplayType => GameEventGameplayType.Sweepstakes;

        protected override bool ShouldShowIntroduction() => State?.WaitingForShowcase ?? false;
        
        public int TotalMilestones => _milestones.Count;
        public int TotalMilestoneScores => _totalMilestoneScores;
        
        public override bool IsUsedInUnifiedWidget => false;

        public override bool HasNoResources()
        {
            return false;
        }
        
        public override string GetScoreIconSpriteNameForCurrentMilestone()
        {
            return GameEventResKeys.ScoreIcon;
        }
        
        public override void AddScore(int delta, bool append = false)
        {
            base.AddScore(delta, append);
            _brainCloudManager.SubmitSweepstakesEventScore(Uid, State.CurrentScore, null, null);
        }

        public int GetTargetScoreForLevel(ProgressionLevelConfig levelConfig)
        {
            return (levelConfig.Difficulty + 1) * (IsMultiplierScoreStreakActive() ? GetScoreMultiplier(true) : 1);
        }
    }
}