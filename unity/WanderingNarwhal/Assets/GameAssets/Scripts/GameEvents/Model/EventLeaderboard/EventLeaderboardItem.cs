using System.Collections.Generic;
using BBB.BrainCloud;
using BBB.Core;
using BebopBee;
using BebopBee.Social;
using RPC.Social;

namespace BBB
{
    public enum PlayerItemType
    {
        Stranger = 0,
        Friend = 1,
        OwnSubmitted = 3,
        OwnUnsubmitted = 4
    }

    public enum PlayerItemChangeAttribute
    {
        Same = 0,
        Up = 1,
        Down = 2
    }

    public static class ChangeAttributeExtensions
    {
        public static PlayerItemChangeAttribute FromScoreComparison(int newScore, int oldScore)
        {
            if (newScore > oldScore)
                return PlayerItemChangeAttribute.Up;
            
            if (newScore < oldScore)
                return PlayerItemChangeAttribute.Down;

            return PlayerItemChangeAttribute.Same;
        }

        public static PlayerItemChangeAttribute FromPlaceComparison(int newPlace, int oldPlace)
        {
            if (newPlace < oldPlace)
                return PlayerItemChangeAttribute.Up;

            if (newPlace > oldPlace)
                return PlayerItemChangeAttribute.Down;

            return PlayerItemChangeAttribute.Same;
        }
    }

    public static class PlayerItemTypeExtensions
    {
        public static string GetAdditionalText(this PlayerItemType itemType)
        {
            switch (itemType)
            {
                case PlayerItemType.OwnSubmitted:
                {
                    return "SUBMITTED_ADD_TEXT_LOC";
                }
                case PlayerItemType.OwnUnsubmitted:
                {
                    return "UNSUBMITTED_ADD_TEXT_LOC";
                }
            }

            return string.Empty;
        }
    }

    public class PlayerEventLeaderboardItem : EventLeaderboardItemBase 
    {
        //server defined data
        public string Uid { get; }
        
        private  string _name;
        public string Name
        {
            get => _name.Split(" ")[0];
            private set => _name = value;
        }
        public string FullName => _name;
        public string AvatarUrl { get; private set; }
        public string Country { get; private set; }
        public int Score { get; private set; }
        public string AvatarFrame { get; private set; }
        public string BadgeUid { get; private set; }

        public PlayerItemType Type { get; private set; }

        public PlayerItemChangeAttribute ChangeAttribute { get; private set; }

        //post assigned data
        public int Place { get; private set; }
        public string AdditionalReward { get; private set; }
        public List<string> AllRewards { get; private set; }
        public Dictionary<string, int> RewardDictionary{ get; private set; }

        public string ScoreOverride { get; private set; }

        public override int PrefabIndex
        {
            get { return 0; }
        }

        public PlayerEventLeaderboardItem(BCLeaderboardUserData leaderboardEntry)
        {
            Uid = leaderboardEntry.UserId;
            Name = leaderboardEntry.UserName;
            AvatarUrl = leaderboardEntry.AvatarUrl;

            Country = leaderboardEntry.SummaryFriendData?.Country ?? "US";
            Score = leaderboardEntry.Score;
            Place = leaderboardEntry.Rank;
            AvatarFrame = leaderboardEntry.SummaryFriendData?.AvatarFrame ?? ProfileUtils.DefaultFrameUid;
            BadgeUid = leaderboardEntry.SummaryFriendData?.BadgeUid ?? string.Empty;
        }
        
        // public PlayerEventLeaderboardItem(BCLeaderboardEntry leaderboardEntry)
        // {
        //     Uid = leaderboardEntry.playerId;
        //     Name = leaderboardEntry.name;
        //     AvatarUrl = leaderboardEntry.pictureUrl;
        //
        //     if (leaderboardEntry.summaryFriendData == null)
        //     {
        //         BDebug.LogError(LogCat.Events, "Summary friend data not found in event leaderboard entry");
        //     }
        //     else if (string.IsNullOrWhiteSpace(leaderboardEntry.summaryFriendData.Value.Country))
        //     {
        //         BDebug.LogError(LogCat.Events, "Country friend data not found in event leaderboard entry");
        //     }
        //     
        //     Country = leaderboardEntry.summaryFriendData?.Country ?? "US";
        //     Score = leaderboardEntry.score;
        //     var dataDict = JsonConvertExts.TryDeserializeObject<Dictionary<string, string>>(leaderboardEntry.data);
        //     if (dataDict.TryGetValue("League", out var strValue))
        //         League = strValue.TryParseToEnum<League>();
        //     else
        //     {
        //         BDebug.LogError(LogCat.Events, "League not found in data entry");
        //     }
        //     Place = -1;
        // }

        public PlayerEventLeaderboardItem(PlayerEventLeaderboardDto leaderboardDto)
        {
            Uid = leaderboardDto.Uid;
            Name = leaderboardDto.Name;
            AvatarUrl = leaderboardDto.AvatarUrl;
            Country = leaderboardDto.Country;
            Score = leaderboardDto.Score;
            Place = -1;
        }

        public void ReplaceName(string name)
        {
            Name = name;
        }
        
        public void UpdateProfile(Profile profile)
        {
            AvatarUrl = profile.Avatar;
            Country = profile.Country;
        }

        public void ResetScore()
        {
            Score = 0;
        }

        public void SetType(PlayerItemType type)
        {
            Type = type;
        }

        public void SetPlaceAndDetermineRewards(int place, League league, CompetitionRewardsContainer rewardsContainer)
        {
            Place = place;

            Dictionary<string, int> rewardsDict = null;
            if (rewardsContainer.HasRewardForPlace(league, place - 1))
            {
                rewardsDict = rewardsContainer.GetRewardForPlace(league, place - 1);
            }
            else
            {
                rewardsDict = rewardsContainer.GetFailureReward(league);
            }

            RewardDictionary = rewardsDict;

            AdditionalReward = null;
            AllRewards ??= new List<string>();
            AllRewards.Clear();
            
            if (rewardsDict is not {Count: > 0}) return;
            
            foreach (var kvp in rewardsDict)
            {
                if (kvp.Value <= 0) continue;
                AllRewards.Add(kvp.Key);
                if (RewardsUtility.IsSpecialReward(kvp.Key))
                    AdditionalReward = kvp.Key;
            }
        }

        public void SetScoreOverride(string scoreOverride)
        {
            ScoreOverride = scoreOverride;
        }

        public void SetChangeAttribute(PlayerItemChangeAttribute attr)
        {
            ChangeAttribute = attr;
        }
    }
}