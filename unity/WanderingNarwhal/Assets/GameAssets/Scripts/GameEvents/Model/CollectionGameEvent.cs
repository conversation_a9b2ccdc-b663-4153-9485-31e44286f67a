using System.Collections;
using System.Collections.Generic;
using BBB.BrainCloud;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.DI;
using BBB.GameAssets.Scripts.Player;
using BebopBee;
using LiveOpsAnalytics;
using PBConfig;
using UnityEngine;

namespace BBB
{
    public partial class CollectionGameEvent : GameEventBase, IGameEventWithMilestones
    {
        private const string NotificationTitleLoc = "GE_DAILYCOLLECT_START_NOTIFICATION_TITLE";
        private const string NotificationMessageLoc = "GE_DAILYCOLLECT_START_NOTIFICATION_MESSAGE";

        protected readonly List<IGameEventMilestone> _milestones = new();
        protected int _totalMilestoneScores;

        // Progress of scores from 0.0 to 1.0 for the entire milestone
        private float ScoreProgress => (State?.CurrentScore ?? 0) / (float)_totalMilestoneScores;
        public override bool IsUsedInUnifiedWidget => true;


        //do not use this
        public override float ProgressRatio => 0f;

        //do not use this
        public override float ProgressRatioLastDelta => 0f;
        public override GameEventGameplayType GameplayType => GameEventGameplayType.Collection;
        private readonly BrainCloudManager _brainCloudManager;
        private readonly IAccountManager _accountManager;

        public CollectionGameEvent(GameEventConfig eventConfig, IContext context, IGlobalGameEventDataProvider dataProvider, IGlobalGameEventChangesNotifier changeNotifier) : 
            base(eventConfig, context, dataProvider, changeNotifier)
        {
            _totalMilestoneScores = 0;

            if (eventConfig.DailyMilestoneConfigs.IsNullOrEmpty())
            {
                BDebug.LogError(LogCat.Collection, "CollectionGameEvent: DailyMilestoneConfigs is null");
                return;
            }
            

            var nextGoal = string.Empty;
            foreach (var dailyMilestoneConfig in eventConfig.DailyMilestoneConfigs)
            {
                var milestone = new CollectionMilestone(eventConfig.Uid, dailyMilestoneConfig, _totalMilestoneScores, nextGoal);
                _milestones.Add(milestone);

                nextGoal = milestone.GoalName;
                _totalMilestoneScores += milestone.Goal;
            }
            _brainCloudManager = context.Resolve<BrainCloudManager>();
            _accountManager = context.Resolve<IAccountManager>();
        }

        protected virtual void CreateMilestones(GameEventConfig eventConfig)
        {
            var nextGoal = string.Empty;
            foreach (var dailyMilestoneConfig in eventConfig.DailyMilestoneConfigs)
            {
                var milestone = new CollectionMilestone(eventConfig.Uid, dailyMilestoneConfig, _totalMilestoneScores, nextGoal);
                _milestones.Add(milestone);

                nextGoal = milestone.GoalName;
                _totalMilestoneScores += milestone.Goal;
            }
        }

        public override void TriggerLiveOpsEvent(string category)
        {
            if (State == null) return;

            var startTime = GetLastStartTime();
            var endTime = GetNextEndTime();
            if (startTime == default || endTime == default) return;

            /*Analytics.LogEventIntoSpecificService<BigFishAnalyticsWrapper>(new LiveOpsEvent(category, Uid,
                _accountManager.BfgBucketId, _brainCloudManager.ProfileId,
                startTime.ToUniversalIso8601(), endTime.ToUniversalIso8601(), 0, 0, CurrentScore, 0));
                */
        }

        public override bool WaitingToShowResult()
        {
            return false;
        }

        public override bool IsReadyToCollect()
        {
            return false;
        }

        public override string GetCurrentRewardFollowingNarrativeId()
        {
            return null;
        }
        
        protected override void Release()
        {
            TriggerLiveOpsEvent(AnalyticNames.End);
            base.Release();
        }

        public override GameEventRewardLocParams GetCurrentRewardLocalizationParams()
        {
            var result = new GameEventRewardLocParams();
            result.HeaderLocKey = null;
            result.TitleLocKey = null;
            result.LocParams = null;
            return result;
        }

        /// <summary>
        /// This method is used in order to add score to the event after match3 victory
        /// it changes the status from active to Accomplished when you reach the goal
        /// </summary>
        public override void AddScore(int delta, bool append = false)
        {
            var status = StatusInternal;

            if (status != GameEventStatus.Active)
            {
                BDebug.LogError(LogCat.General, $"Trying to add score to not active event {EventConfig.Uid}");
                //return;
            }

            var prevMilestoneIndex = GetCurrentMilestoneIndex();
            var prevScore = State.CurrentScore;
            if (delta > 0)
            {
                SetScore(Mathf.Min(State.CurrentScore + delta, _totalMilestoneScores));
            }

            int resultDelta = State.CurrentScore - prevScore;
            State.LastAddedScoreDelta = append ? State.LastAddedScoreDelta + resultDelta : resultDelta;

            if (State.CurrentScore >= _totalMilestoneScores)
            {
                SetScore(_totalMilestoneScores);
                State.Status = (int)GameEventStatus.Accomplished;
            }

            if (delta > 0)
            {
                LastAddedScoreToShow = append ? LastAddedScoreToShow + resultDelta : resultDelta;
            }
        }

        protected override GameEventStatus GetExpirationStatus()
        {
            return GameEventStatus.Failed;
        }

        protected override void HandleExpiration()
        {
        }

        protected override void HandleLaunch()
        {
            TriggerLiveOpsEvent(AnalyticNames.Start);
        }

        public override void DebugSetScore(int score)
        {
            State.LastAddedScoreDelta = 0;
            SetScore(score);

            if (State.CurrentScore < _totalMilestoneScores)
            {
                State.Status = (int)GameEventStatus.Active;
            }
            else
            {
                State.Status = (int)GameEventStatus.Accomplished;
            }
        }

        protected override bool IsReadyToRelease()
        {
            var status = StatusInternal;

            if (status == GameEventStatus.Failed)
                return true;

            var utcNow = GlobalDataProvider.GetCurrentUtcDateTime();

            return status == GameEventStatus.AccomplishedAndAllMiletonesCollected && !DoesMyTimeSpanContain(utcNow);
        }

        public GameEventCurrencyFlowData? GetCurrencyFlowAnalyticsData(int milestoneIndex)
        {
            var milestone = GetMilestoneByIndex(milestoneIndex);
            if (milestone == null)
            {
                return null;
            }
            
            var milestoneTarget = milestone.GetMilestoneTarget();
            var family = milestoneTarget.ToFamilyName();

            return new GameEventCurrencyFlowData
            {
                Category = CurrencyFlow.GameEvents.Name,
                Family = family,
                Item = milestoneIndex == GetLastMilestoneIndex()
                    ? CurrencyFlow.GameEvents.MilestoneFinal
                    : string.Format(CurrencyFlow.GameEvents.Milestone, milestoneIndex)
            };
        }

        /// <summary>
        /// Returns the reward depending on status and notifies about victory reward being claimed
        /// </summary>
        /// <returns>Dictionary of reward type key to count value</returns>
        public override Dictionary<string, int> ClaimReward()
        {
            var status = StatusInternal;
            switch (status)
            {
                case GameEventStatus.Accomplished:
                {
                    State.Status = (int)GameEventStatus.AccomplishedAndAllMiletonesCollected;
                    ChangeNotifier.OnAccomplishClaim(this);
                    return null;
                }
                default:
                {
                    BDebug.LogError(LogCat.General, $"Can not claim game event {EventConfig.Uid} reward in status {status}");
                    return null;
                }
            }
        }

        public override int GenerateScore(Stage stage)
        {
            var metamilestone = GlobalDataProvider.GetMetaConfig();
            //static method here is needed to run in the editor
            return GameEventScoreRandomGen.GenerateForCompletionEvent(EventConfig.Uid, metamilestone, stage,
                MinMaxScore, ScoreProgress, TimeProgress);
        }

        public override bool IsWon()
        {
            return IsWon_Internal();
        }

        public override (string, string) GetDialogueHeader()
        {
            return ("DIALOG_HEADER_STORIES", DisplayName);
        }

        public override bool CanShowInScreen(ScreenType screenType)
        {
            return true;
        }

        private bool IsWon_Internal()
        {
            var status = StatusInternal;
            return status == GameEventStatus.AccomplishmentRewardCollected
                   || status == GameEventStatus.AccomplishedTripstagramViewed
                   || status == GameEventStatus.Accomplished;
        }

        public override string GetStartNotificationTitle()
        {
            return LocalizationManager.getLocalizedText(NotificationTitleLoc);
        }

        public override string GetStartNotificationMessage()
        {
            return LocalizationManager.getLocalizedText(NotificationMessageLoc);
        }

        IGameEventMilestone IGameEventWithMilestones.GetMilestoneByIndex(int index)
        {
            return GetMilestoneByIndex(index);
        }

        public IGameEventMilestone GetMilestoneByIndex(int index)
        {
            if (_milestones.IsNullOrEmpty())
            {
                return null;
            }
                
            index = Mathf.Clamp(index, 0, _milestones.Count - 1);
            return index >= 0 && index < _milestones.Count ? _milestones[index] : null;
        }

        public MilestoneTarget GetCurrentMilestoneTarget()
        {
            var milestone = GetMilestoneByIndex(GetCurrentMilestoneIndex());
            return milestone?.GetMilestoneTarget() ?? MilestoneTarget.None;
        }

        public override string GetScoreIconSpriteNameForCurrentMilestone()
        {
            var milestone = GetMilestoneByIndex(GetCurrentMilestoneIndex());
            if (milestone == null)
            {
                return string.Empty;
            }
            
            var targetName = milestone.GoalName;
            return $"target_{targetName}";
        }

        public IGameEventMilestone GetLastMilestone()
        {
            if (_milestones == null || _milestones.Count == 0)
            {
                return null;
            }

            return _milestones[^1];
        }


        public int GetLastMilestoneIndex()
        {
            if (_milestones.Count > 0)
                return _milestones.Count - 1;
            return 0;
        }

        public int GetCurrentMilestoneIndex()
        {
            return GetMilestoneIndexForScore(CurrentScore);
        }

        public int GetPreviousMilestoneIndex()
        {
            return GetMilestoneIndexForScore(PreviousTotalScore);
        }

        private int GetMilestoneIndexForScore(int score)
        {
            if (_milestones.IsNullOrEmpty())
                return 0;

            for (var i = _milestones.Count - 1; i >= 0; --i)
            {
                if (score >= _milestones[i].StartScore)
                    return i;
            }

            return 0;
        }

        public override int GetGoalToNextMilestone()
        {
            var milestone = GetMilestoneByIndex(GetCurrentMilestoneIndex());
            if (milestone == null)
            {
                return 0;
            }
            
            var toNextGoal = milestone.GetGoal() - milestone.GetRelativeScore(CurrentScore) - 1;
            if (toNextGoal < 0)
            {
                toNextGoal = 0;
            }

            return toNextGoal;
        }

        public override bool HasNoResources()
        {
            return true;
        }
    }
}