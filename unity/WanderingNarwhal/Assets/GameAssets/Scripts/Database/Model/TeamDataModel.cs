using System.Collections.Generic;
using BBB;
using Realms;
using TeamType = GameAssets.Scripts.SocialScreens.Teams.TeamInfo.TeamType;

namespace GameAssets.Scripts.Database.Model
{
    public class TeamDataModel : EmbeddedObject
    {
        public string TeamUid { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Icon { get; set; }
        public IList<TeamMemberDataModel> Members { get; }
        
        public TeamType TeamType
        {
            get => (TeamType)TeamTypeInt;
            set => TeamTypeInt = (int)value;
        }

        private int TeamTypeInt { get; set; }
        public int Score { get; set; }
        public int Activity { get; set; }
        public long JoinedAt { get; set; }
        public string Country { get; set; }
        public int RequiredLevel { get; set; }
        
        public TeamDataModel(TeamData teamData)
        {
            if (teamData == null)
            {
                return;
            }
            
            TeamUid = teamData.TeamUid;
            Name = teamData.Name;
            Description = teamData.Description;
            Icon = teamData.Icon;
            TeamType = teamData.TeamType;
            Score = teamData.Score;
            Activity = teamData.Activity;
            JoinedAt = teamData.JoinedAt;
            Country = teamData.Country;
            RequiredLevel = teamData.RequiredLevel;
            
            foreach (var teamMember in teamData.Members)
            {
                Members.Add(new TeamMemberDataModel(teamMember));
            }
        }

        public TeamData ToTeamData() => new()
        {
            TeamUid = TeamUid,
            Name = Name,
            Description = Description,
            Icon = Icon,
            TeamType = TeamType,
            Score = Score,
            Activity = Activity,
            JoinedAt = JoinedAt,
            Country = Country,
            RequiredLevel = RequiredLevel,
            Members = ToTeamMemberDataList()
        };

        public TeamDataModel() 
        {
        }

        private List<TeamMemberData> ToTeamMemberDataList()
        {
            var teamMembers = new List<TeamMemberData>();
            
            foreach (var members in Members)
            {
                teamMembers.Add(members.ToTeamMemberData());
            }

            return teamMembers;
        }
    }
}