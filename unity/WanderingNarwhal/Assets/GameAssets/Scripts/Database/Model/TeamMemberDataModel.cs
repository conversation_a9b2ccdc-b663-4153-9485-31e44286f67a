using BBB;
using Realms;

namespace GameAssets.Scripts.Database.Model
{
    public class TeamMemberDataModel : EmbeddedObject
    {
        public string Uid { get; set; }
        public string Name { get; set; }
        public string Avatar { get; set; }
        public int Trophies { get; set; }
        public long LastActivityTimestamp { get; set; }
        public string Country { get; set; }
        public bool IsAdmin { get; set; }
        public bool IsOwner { get; set; }
        public string AvatarDecorations { get; set; }
        public int HelpCount { get; set; }

        public TeamMemberDataModel(TeamMemberData teamMemberData)
        {
            Uid = teamMemberData.Uid;
            Name = teamMemberData.Name;
            Avatar = teamMemberData.Avatar;
            Trophies = teamMemberData.Trophies;
            LastActivityTimestamp = teamMemberData.LastActivityTimestamp;
            Country = teamMemberData.Country;
            IsAdmin = teamMemberData.IsAdmin;
            IsOwner = teamMemberData.IsOwner;
            HelpCount = teamMemberData.HelpCount;
        }
        
        public TeamMemberData ToTeamMemberData() => new()
        {
            Uid = Uid,
            Name = Name,
            Avatar = Avatar,
            Trophies = Trophies,
            LastActivityTimestamp = LastActivityTimestamp,
            Country = Country,
            IsAdmin = IsAdmin,
            IsOwner = IsOwner,
            HelpCount = HelpCount
        };

        public TeamMemberDataModel()
        {
        }
    }
}