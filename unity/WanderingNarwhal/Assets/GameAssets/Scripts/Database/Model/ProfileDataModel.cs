using System;
using System.Collections.Generic;
using BBB;
using BebopBee.Social;
using Newtonsoft.Json;
using Realms;

namespace GameAssets.Scripts.Database.Model
{
    public class ProfileDataModel : RealmObject
    {
        public string Uid { get; set; }
        public string Avatar { get; set; }
        public string FacebookAvatar { get; set; }
        public string Name { get; set; }
        public string DisplayName { get; set; }
        public string Country { get; set; }
        public string LastUnlockedLocationId { get; set; }
        public string HighestPassedLevelId { get; set; }
        public int LastActiveTimeStamp { get; set; }
        public TeamDataModel TeamDataModel { get; set; }
        public bool IsDisplayNameOverriden { get; set; }
        public string FacebookName { get; set; }
        public string FacebookId { get; set; }
        public string FacebookEmail { get; set; }
        public string ProvidedEmail { get; set; }
        public string AppleId { get; set; }
        public string AppleGivenName { get; set; }
        public string AppleFamilyName { get; set; }
        public string AppleEmail { get; set; }
        public string GoogleId { get; set; }
        public string GoogleEmail { get; set; }
        public string GoogleDisplayName { get; set; }
        public string GoogleAvatar { get; set; }
        public string PhoneNumber { get; set; }
        public int CurrentLevelStage { get; set; }
        public bool NotificationsEnabled { get; set; }
        public int HelpCount { get; set; }
        public string SignedDisplayName { get; set; }
        public string SignedAvatar { get; set; }
        public string SignedEmail { get; set; }
        public bool SdbEnabled { get; set; }
        public bool ChallengesEnabled { get; set; }
        public int OldTrophies { get; set; }
        public int Trophies { get; set; }
        public byte DetailedAnalyticsState { get; set; }

        // New fields from BCSummaryFriendData
        public double? InstallDate { get; set; }
        public int? FirstTryWins { get; set; }
        public int? HelpsReceived { get; set; }
        public int? ScenesCompleted { get; set; }
        public int? SetsCompleted { get; set; }
        public int? LeaguesWon { get; set; }
        public bool? IsBot { get; set; }
        
        public string AvatarFrame { get; set; }
        public string BadgeUid { get; set; }
        public string NameStyle { get; set; }

        [Obsolete]
        public string LastLevelPassedId { get; set; }
        [Obsolete]
        public string CurrentLevelId { get; set; }
        
        public ProfileDataModel() { }

        public ProfileDataModel(string userId, string displayName)
        {
            Uid = userId;
            DisplayName = displayName;
            TeamDataModel = new TeamDataModel();
        }

        public ProfileDataModel(Dictionary<string, object> profile)
        {
            SetData(profile);
        }

        public void SetData(Dictionary<string, object> profile)
        {
            Name = ProfileUtils.GetConvertToString(profile, "name");
            Uid = ProfileUtils.GetConvertToString(profile, "uid");
            Avatar = ProfileUtils.GetConvertToString(profile, "avatar");
            DisplayName = ProfileUtils.GetConvertToString(profile, "display_name");
            LastUnlockedLocationId = ProfileUtils.IsStringValueNull(profile, "last_unlocked_location_id") ? LocationConstants.DefaultLocation : ProfileUtils.GetConvertToString(profile, "last_unlocked_location_id");
            HighestPassedLevelId = ProfileUtils.IsStringValueNull(profile, "highest_passed_level_id") ? ProfileUtils.DefaultLevel : ProfileUtils.GetConvertToString(profile, "highest_passed_level_id");
            Country = ProfileUtils.IsStringValueNull(profile, "country") ? PlatformUtil.GetCurrentCountryCode() : ProfileUtils.GetConvertToString(profile, "country");
            FacebookName = ProfileUtils.GetConvertToString(profile, "fb_name");
            FacebookAvatar = ProfileUtils.GetConvertToString(profile, "fb_avatar");
            FacebookId = ProfileUtils.GetConvertToString(profile, "fb_id");
            FacebookEmail = ProfileUtils.GetConvertToString(profile, "email");
            ProvidedEmail = ProfileUtils.GetConvertToString(profile, "provided_email");
            PhoneNumber = ProfileUtils.GetConvertToString(profile, "phone_number");
            CurrentLevelStage = ProfileUtils.GetConvertToInt(profile, "current_level_stage");
            AppleId = ProfileUtils.GetConvertToString(profile, "apple_id");
            AppleEmail = ProfileUtils.GetConvertToString(profile, "apple_email");
            AppleGivenName = ProfileUtils.GetConvertToString(profile, "apple_given_name");
            AppleFamilyName = ProfileUtils.GetConvertToString(profile, "apple_family_name");
            GoogleId = ProfileUtils.GetConvertToString(profile, "google_id");
            GoogleEmail = ProfileUtils.GetConvertToString(profile, "google_email");
            GoogleDisplayName = ProfileUtils.GetConvertToString(profile, "google_display_name");
            GoogleAvatar = ProfileUtils.GetConvertToString(profile, "google_avatar");
            IsDisplayNameOverriden = ProfileUtils.GetConvertToBool(profile, "is_display_name_overridden");
            NotificationsEnabled = ProfileUtils.GetConvertToBool(profile, "notifications_enabled");
            TeamDataModel = new TeamDataModel(JsonConvert.DeserializeObject<TeamData>(ProfileUtils.GetConvertToString(profile, "current_team")));
            HelpCount = ProfileUtils.GetConvertToInt(profile, "help_count");
            SignedDisplayName = ProfileUtils.GetConvertToString(profile, "signed_display_name");
            SignedAvatar = ProfileUtils.GetConvertToString(profile, "signed_avatar");
            SignedEmail = ProfileUtils.GetConvertToString(profile, "signed_email");
            LastActiveTimeStamp = (int)ProfileUtils.GetConvertToDouble(profile, "last_action_time");
            ChallengesEnabled = ProfileUtils.GetConvertToBool(profile, "challenges_enabled");
            
            // Set new fields from BCSummaryFriendData
            InstallDate = profile.ContainsKey("install_date") ? ProfileUtils.GetConvertToDouble(profile, "install_date") : null;
            FirstTryWins = profile.ContainsKey("first_try_wins") ? ProfileUtils.GetConvertToInt(profile, "first_try_wins") : null;
            HelpsReceived = profile.ContainsKey("helps_received") ? ProfileUtils.GetConvertToInt(profile, "helps_received") : null;
            ScenesCompleted = profile.ContainsKey("scenes_completed") ? ProfileUtils.GetConvertToInt(profile, "scenes_completed") : null;
            SetsCompleted = profile.ContainsKey("sets_completed") ? ProfileUtils.GetConvertToInt(profile, "sets_completed") : null;
            LeaguesWon = profile.ContainsKey("leagues_won") ? ProfileUtils.GetConvertToInt(profile, "leagues_won") : null;
            IsBot = profile.ContainsKey("is_bot") && ProfileUtils.GetConvertToBool(profile, "is_bot");
            
            AvatarFrame = ProfileUtils.GetConvertToString(profile, "avatar_frame");
            BadgeUid = ProfileUtils.GetConvertToString(profile, "badge_uid");
            NameStyle = ProfileUtils.GetConvertToString(profile, "name_style");
            
            SetTrophies(profile);
        }

        private void SetTrophies(Dictionary<string, object> profile)
        {
            var oldTrophiesObj = profile.GetValueOrDefault("trophies");

            switch (oldTrophiesObj)
            {
                case null:
                {
                    OldTrophies = 0;
                    break;
                }
                case string str:
                {
                    int.TryParse(str, out var value);
                    OldTrophies = value;
                    break;
                }
                case int integer:
                {
                    OldTrophies = integer;
                    break;
                }
            }

            var newTrophiesObj = profile.GetValueOrDefault("new_trophies");

            Trophies = newTrophiesObj switch
            {
                null => 0,
                string str => int.Parse(str),
                int integer => integer,
                _ => Trophies
            };
        }
    }
}