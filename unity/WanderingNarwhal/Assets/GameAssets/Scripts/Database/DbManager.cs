using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using BBB;
using BBB.BrainCloud;
using BBB.Core;
using Realms;
using UnityEngine;

namespace GameAssets.Scripts.Database
{
    public static class DbManager
    {
        private const int TeamScoreChangeSchemeVersion = 4;
        
        private static Realm _database;

        private static Realm Database
        {
            get
            {
                if (_database != null)
                    return _database;
                
                InitializeDatabase();
                
                return _database;
            }
        }
        
        static DbManager()
        {
            InitializeDatabase();
        }

        /// <summary>
        /// Initializes the database.
        /// </summary>
        /// <remarks>
        /// For schema changes logs see the "DB Migration Notes" sheet
        /// <see href="https://t.ly/XJh5y"></see> .
        /// </remarks>
        private static void InitializeDatabase()
        {
            var config = new RealmConfiguration
            {
                SchemaVersion = 16,
                MigrationCallback = (migration, oldSchemaVersion) =>
                {
                    if (oldSchemaVersion >= TeamScoreChangeSchemeVersion)
                        return;

                    const string bCTeamEventDataClassName = nameof(BCTeamEventData);
                    const string teamScorePropertyName = nameof(BCTeamEventData.TeamScore);

                    var oldSchema = migration.OldRealm.Schema;
                    if (oldSchema.All(s => s.Name != bCTeamEventDataClassName))
                    {
                        BDebug.LogWarning($"{bCTeamEventDataClassName} does not exist in the old schema. Skipping migration for this class.");
                        return;
                    }

                    var oldEntries = migration.OldRealm.DynamicApi.All(bCTeamEventDataClassName);
                    var newEntries = migration.NewRealm.All<BCTeamEventData>();

                    var oldEntriesCount = oldEntries.Count();
                    var newEntriesCount = newEntries.Count();

                    if (oldEntriesCount != newEntriesCount)
                    {
                        BDebug.LogWarning($"Mismatched counts: Old entries ({oldEntriesCount}) vs. New entries ({newEntriesCount}). Skipping migration for inconsistent data.");
                        return;
                    }

                    for (var i = 0; i < newEntriesCount; i++)
                    {
                        var oldEntry = oldEntries.ElementAtOrDefault(i);
                        var newEntry = newEntries.ElementAtOrDefault(i);

                        if (oldEntry == null || newEntry == null)
                        {
                            BDebug.LogWarning($"Entry mismatch or null at index {i}. Skipping this entry.");
                            continue;
                        }

                        try
                        {
                            var teamScore = oldEntry.DynamicApi.Get<int?>(teamScorePropertyName) ?? 0;
                            newEntry.TeamScore = teamScore;
                        }
                        catch (KeyNotFoundException)
                        {
                            BDebug.LogError(LogCat.General, $"Property {teamScorePropertyName} not found in the old schema. Index: {i}");
                        }
                        catch (InvalidCastException)
                        {
                            BDebug.LogError(LogCat.General, $"Property {teamScorePropertyName} has an invalid type in the old schema. Index: {i}");
                        }
                    }
                }
            };
            _database = Realm.GetInstance(config);
        }

        public static void SaveData<T>(T dataToSave) where T : RealmObject
        {
            try
            {
                Database.Write(() =>
                {
                    Database?.Add(dataToSave, update: true);
                });
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error saving data: {ex.Message}");
            }
        }

        public static T LoadDataById<T>(string id) where T : RealmObject
        {
            try
            {
                return Database.Find<T>(id);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error loading data: {ex.Message}");
                return null;
            }
        }
        
        public static T LoadData<T>() where T : RealmObject
        {
            try
            {
                return Database.All<T>().FirstOrDefault();
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error loading data: {ex.Message}");
                return null;
            }
        }
        
        public static List<T> LoadAll<T>() where T : RealmObject
        {
            try
            {
                return Database.All<T>().ToList();
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error loading data: {ex.Message}");
                return null;
            }
        }
        
        public static void SaveAll<T>(List<T> dataToSave) where T : RealmObject
        {
            try
            {
                Database.Write(() =>
                {
                    foreach (var datumToSave in dataToSave)
                    {
                        Database.Add(datumToSave, update: true);
                    }
                });
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error saving data: {ex.Message}");
            }
        }

        public static void DeleteDataById<T>(string id) where T : RealmObject
        {
            try
            {
                var data = Database.Find<T>(id);
                if (data != null)
                {
                    Database.Write(() =>
                    {
                        Database.Remove(data);
                    });
                }
                else
                {
                    LogNotFoundWarning(id);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error deleting data: {ex.Message}");
            }
        }
        
        public static void UpdateDataById<T>(string id, Action<T> updateAction) where T : RealmObject
        {
            try
            {
                var data = Database.Find<T>(id);

                if (data != null)
                {
                    Database.Write(() =>
                    {
                        updateAction(data);
                    });
                }
                else
                {
                    LogNotFoundWarning(id);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error updating data: {ex.Message}");
            }
        }
        
        public static void UpdateData<T>(Action<T> updateAction) where T : RealmObject
        {
            try
            {
                var data = Database.All<T>().FirstOrDefault();

                if (data != null)
                {
                    Database.Write(() =>
                    {
                        updateAction(data);
                    });
                }
                else
                {
                    LogNotFoundWarning();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error updating data: {ex.Message}");
            }
        }

        public static void DropDatabase()
        {
            Database.Write(() =>
            {
                Database.RemoveAll();
            });
        }
        
        public static void DropCollection(RealmObject collection)
        {
            Database.Write(() =>
            {
                Database?.Remove(collection);
            });
        }
        
        public static void DropAllCollections<T>() where T : RealmObject
        {
            Database.Write(() =>
            {
                Database.RemoveAll<T>();
            });
        }
        
        private static void LogNotFoundWarning(string key = null)
        {
            var message = "No data found in Database";
            if (!key.IsNullOrEmpty())
            {
                message += $" with id: {key}";
            }
           
            Debug.LogWarning(message);
        }
    }
}