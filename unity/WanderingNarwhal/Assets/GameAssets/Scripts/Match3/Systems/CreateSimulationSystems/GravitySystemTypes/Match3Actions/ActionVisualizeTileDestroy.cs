using BBB.Match3.Debug;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionVisualizeTileDestroy : Match3ActionBase
    {
        private readonly Coords _target;
        private readonly HitWaitParams _hitWaitParams;
        private readonly int _tileId;

        public ActionVisualizeTileDestroy(Coords target, Tile tile, 
            HitWaitParams hitWaitParams)
        {
            if (tile.IsNull())
            {
#if BBB_LOG
                M3Debug.LogError("[LA]: RemoveTile. Tile is null!!!");
#endif
                return;
            }

            _target = target;
            _hitWaitParams = hitWaitParams;
            _tileId = tile.Id;
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams, _tileId, _target);
            
            AffectedCoords.Add(_target);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ReleasedCoords.Add(_target);
            
            var targetCell = grid.GetCell(_target);
            var tileToRemove = targetCell.Tile;
            if (SimulationPlayUtils.CheckTileForRemoval(tileToRemove, _target))
                return;
        
            if (!proxy.GoalsSystem.IsTileShouldSkipDestroyAnim(_tileId))
            {
                var view = proxy.TileController.GetTileViewByCoord(targetCell.Coords, false);
                if (view != null)
                {

                    var matchSetting = proxy.Settings.TileDestroyByBooster;
                    var animParams = _hitWaitParams?.DamageSource.ToAnimParams() ?? TileLayerViewAnimParams.None;
                    PreDestroyTweener preDestroyTweener = default;
                
                    if (tileToRemove.IsSimple())
                    {
                        // Do regular shrink+destroy animation before destroying view, only for simple tiles

                        proxy.TileTickPlayer.BoardObjectFactory.CreateStaticOccupier(matchSetting.BusyTime, targetCell.Coords.ToUnityVector2());
                        var tweener = view.ShrinkScaleTo(matchSetting.Duration, matchSetting.TargetScale, matchSetting.ScaleCurve);
                    
                        preDestroyTweener = new PreDestroyTweener
                        {
                            Tweener = tweener,
                            OnTweenerComplete = () =>
                            {
                                OnMoveDone(view);
                            }
                        };
                    }
                    else switch (tileToRemove.Speciality)
                    {
                        case TileSpeciality.Toad or TileSpeciality.SlotMachine:
                        {
                            // TODO: Find a more generic way to do this, by delegating this to the tile itself (instead of
                            // this logic being applied by checking tile speciality
                            var destroyBusyTime = tileToRemove.Speciality == TileSpeciality.Toad
                                ? proxy.Settings.ToadDestroyBusyTime
                                : proxy.Settings.SlotMachineDestroyBusyTime;
                            var sizeX = tileToRemove.GetParam(TileParamEnum.SizeX);
                            var sizeY = tileToRemove.GetParam(TileParamEnum.SizeY);
                            proxy.TileTickPlayer.BoardObjectFactory.CreateOccupiersOver(destroyBusyTime, view.Coords,
                                view.Coords + new Coords(sizeX - 1, sizeY - 1));
                            break;
                        }
                        case TileSpeciality.GoldenScarab:
                        {
                            var goldenScarabDestroyBusyTime = proxy.Settings.GoldenScarabDestroyBusyTime;
                            proxy.TileTickPlayer.BoardObjectFactory.CreateStaticOccupier(goldenScarabDestroyBusyTime, view.Coords.ToUnityVector2());
                            break;
                        }
                        case TileSpeciality.Monkey:
                        {
                            var monkeyDestroyBusyTime = proxy.Settings.MonkeyDestroyBusyTime;
                            proxy.TileTickPlayer.BoardObjectFactory.CreateStaticOccupier(monkeyDestroyBusyTime, view.Coords.ToUnityVector2());
                            break;
                        }
                        default:
                        {
                            proxy.TileTickPlayer.BoardObjectFactory.CreateStaticOccupier(matchSetting.BusyTime, targetCell.Coords.ToUnityVector2());
                            break;
                        }
                    }
                    
                    proxy.TileController.VisualizeTileDestruction(targetCell, animParams, preDestroyTweener);
                }
            }
    
            void OnMoveDone(TileView tileView)
            {
                tileView.Hide();
            }
        }

        protected override string GetMembersString()
        {
            return $"tileId={_tileId} coords={_target}";
        }
    }
}
