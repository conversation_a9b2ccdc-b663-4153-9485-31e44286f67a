using System;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    [Flags]
    public enum GeneralizedLayer : ulong
    {
        None,
        GrassOne,
        GrassTwo,
		Simple,
        HorizontalLb,
        VerticalLb,
        Bomb,
        ColorBomb,
        CrateOne,
        CrateTwo,
        CrateThree,
        ColorCrateOne,
        ColorCrateTwo,
        ColorCrateThree,
        Squid,
        Toad,
        Balloon,
        EggOne,
        EggTwo,
		Bird,
        SheepOne,
        SheepTwo,
        SheepThree,
		Banana,
		Monkey,
        WatermelonOne,
        WatermelonTwo,
        WatermelonThree,
        Pinata,
        FrameOne,
        Animal,
        GameEventLabel,
		HenOne,
		HenTwo,
		Chicken,
		Hive,
		ExhaustedHive,
		Bee,
		MoleHidden,
		MoleShown,
		VaseOne,
		VaseTwo,
		VaseThree,
        ChainOne,
        ChainTwo,
        ChainThree,
        IceOne,
        IceTwo,
        IceThree,
        Sand,
		Ivy,
        Skunk,
        Propeller,
        MoneyBag,
        FrameTwo,
        FrameThree,
        Tnt,
        DropItem,
        BowlingCurtain,
        Bowling,
        Bush,
        Soda,
        MagicHat,
        ExhaustedMagicHat,
        Safe,
        FlowerPotOne,
        FlowerPotTwo,
        Petal,
        IceBar,
        DynamiteStick,
        GiantPinata,
        MetalBar,
        Shelf,
        JellyFish,
        DestructibleWall,
        GoldenScarabEmpty,
        GoldenScarabFull,
        Gondola,
        ReachedGondola,
        TukTuk,
        ShelfEmpty,
        FireWorks,
        SlotMachine,
        SlotMachineCover,
        FrameNearAnimal,
    }
}
