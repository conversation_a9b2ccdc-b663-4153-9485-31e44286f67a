using System.Collections.Generic;
using BBB.CellTypes;
using GameAssets.Scripts.Match3.Logic.Tiles;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public static class CellLayerAnalyzer
    {
        /// <summary>
        /// Returns generalized layers of the cell derived from cell and tile states
        /// The order of layers is bottom to top from the perspective of damagability
        /// </summary>
        /// <param name="cell">Some cell</param>
        /// <returns>Enumerable of enum values of generalized layers</returns>
        public static IEnumerable<GeneralizedLayer> GetGeneralizedLayers(this Cell cell)
        {
            var mainCellOverlay = cell.GetMainCellReference(out var offset, isCellOverlay: true);
            var mainCell = cell.GetMainCellReference(out offset, isCellOverlay: false);
            if (mainCellOverlay.IsAnyOf(CellState.Tnt))
                yield return GeneralizedLayer.Tnt;

            if (cell.IsAnyOf(CellState.BackOne) || cell.IsAnyOf(CellState.BackDouble))
                yield return GeneralizedLayer.GrassOne;

            if (cell.IsAnyOf(CellState.BackDouble))
                yield return GeneralizedLayer.GrassTwo;
            
            if (cell.IsAnyOf(CellState.Petal))
                yield return GeneralizedLayer.Petal;
            
            if (cell.IsAnyOf(CellState.DestructibleWall))
                yield return GeneralizedLayer.DestructibleWall;

            if (mainCell.HasTile())
            {
                var tile = mainCell.Tile;
                if (tile.IsSimple())
                    yield return GeneralizedLayer.Simple;

                var spec = tile.Speciality;

                switch (spec)
                {
                    case TileSpeciality.Bowling:
                    {
                        var bowlingCurtainCount = tile.GetParam(TileParamEnum.BowlingOpened);
                        if (bowlingCurtainCount == 0)
                        {
                            yield return GeneralizedLayer.BowlingCurtain;
                        }
                        yield return GeneralizedLayer.Bowling;
                    }
                        break;
                    case TileSpeciality.Bush:
                        yield return GeneralizedLayer.Bush;
                        break;
                    case TileSpeciality.Soda:
                        yield return GeneralizedLayer.Soda;
                        break;
                    case TileSpeciality.DynamiteBox:
                        yield return GeneralizedLayer.DynamiteStick;
                        break;
                    case TileSpeciality.Safe:
                        yield return GeneralizedLayer.Safe;
                        break;
                    case TileSpeciality.IceBar:
                        yield return GeneralizedLayer.IceBar;
                        break;
                    case TileSpeciality.MetalBar:
                        yield return GeneralizedLayer.MetalBar;
                        break;
                    case TileSpeciality.GiantPinata:
                        yield return GeneralizedLayer.GiantPinata;
                        break;
                    case TileSpeciality.Shelf:
                        var shelfCount = tile.GetParam(TileParamEnum.AdjacentHp);
                        if (shelfCount == ShelfTile.ShelfHp)
                        {
                            yield return GeneralizedLayer.Shelf;
                        }
                        else
                        {
                            yield return GeneralizedLayer.ShelfEmpty;
                        }
                        break;
                    case TileSpeciality.JellyFish:
                        yield return GeneralizedLayer.JellyFish;
                        break;
                    case TileSpeciality.GoldenScarab:
                    {
                        var adjacentHp = tile.GetParam(TileParamEnum.GoldenScarabCount);
                        if (adjacentHp >= 1)
                            yield return GeneralizedLayer.GoldenScarabFull;
                        else
                        {
                            yield return GeneralizedLayer.GoldenScarabEmpty;
                        }
                        break;
                    }
                    case TileSpeciality.Gondola:
                    {
                        if (tile.GetParam(TileParamEnum.GondolaReached) == 1)
                            yield return GeneralizedLayer.ReachedGondola;
                        else
                        {
                            yield return GeneralizedLayer.Gondola;
                        }
                        break;
                    }
                    case TileSpeciality.TukTuk:
                        yield return GeneralizedLayer.TukTuk;
                        break;
                    case TileSpeciality.FireWorks:
                        yield return GeneralizedLayer.FireWorks;
                        break;
                    case TileSpeciality.SlotMachine:
                    {
                        var layerCount = tile.GetParam(TileParamEnum.AdjacentHp);
                        yield return layerCount >= SlotMachineTile.SlotMachineHp ? GeneralizedLayer.SlotMachineCover : GeneralizedLayer.SlotMachine;
                        break;
                    }
                    case TileSpeciality.DropItem:
                        yield return GeneralizedLayer.DropItem;
                        break;
                    case TileSpeciality.Skunk:
                        yield return GeneralizedLayer.Skunk;
                        break;
                    case TileSpeciality.RowBreaker:
                        yield return GeneralizedLayer.HorizontalLb;
                        break;
                    case TileSpeciality.ColumnBreaker:
                        yield return GeneralizedLayer.VerticalLb;
                        break;
                    case TileSpeciality.Bomb:
                        yield return GeneralizedLayer.Bomb;
                        break;
                    case TileSpeciality.ColorBomb:
                        yield return GeneralizedLayer.ColorBomb;
                        break;
                    case TileSpeciality.Propeller:
                        yield return GeneralizedLayer.Propeller;
                        break;
                    case TileSpeciality.MoneyBag:
                        if(tile.IsNoneOf(TileState.VaseMod))
                            yield return GeneralizedLayer.MoneyBag;
                        break;
                    case TileSpeciality.Sticker:
                    {
                        var adjacentHp = tile.GetParam(TileParamEnum.AdjacentHp);
                        if (adjacentHp >= 1)
                            yield return GeneralizedLayer.CrateOne;
                        if (adjacentHp >= 2)
                            yield return GeneralizedLayer.CrateTwo;
                        if(adjacentHp >= 3)
                            yield return GeneralizedLayer.CrateThree;
                        break;
                    }
                    case TileSpeciality.ColorCrate:
                    {
                        var layerCount = tile.GetParam(TileParamEnum.AdjacentHp);
                        if (layerCount >= 1)
                            yield return GeneralizedLayer.ColorCrateOne;
                        if (layerCount >= 2)
                            yield return GeneralizedLayer.ColorCrateTwo;
                        if(layerCount >= 3)
                            yield return GeneralizedLayer.ColorCrateThree;
                        break;
                    }
                    case TileSpeciality.Squid:
                        yield return GeneralizedLayer.Squid;
                        break;
                    case TileSpeciality.Toad:
                        yield return GeneralizedLayer.Toad;
                        break;
                    case TileSpeciality.MagicHat:
                        if(tile.GetParam(TileParamEnum.MagicHatOutOfRabbitsFlag) == 1)
                            yield return GeneralizedLayer.ExhaustedMagicHat;
                        else
                        {
                            yield return GeneralizedLayer.MagicHat;
                        }
                        break;
                    case TileSpeciality.Litter:
                        yield return GeneralizedLayer.Balloon;
                        break;
                    case TileSpeciality.Bird:
                        yield return GeneralizedLayer.Bird;
                        break;
                    case TileSpeciality.Sheep:
                    {
                        var layerCount = tile.GetParam(TileParamEnum.AdjacentHp);
                        if (layerCount >= 1)
                            yield return GeneralizedLayer.SheepOne;
                        if (layerCount >= 2)
                            yield return GeneralizedLayer.SheepTwo;
                        if(layerCount >= 3)
                            yield return GeneralizedLayer.SheepThree;
                        break;
                    }
                    case TileSpeciality.Banana:
                        yield return GeneralizedLayer.Banana;
                        break;
                    case TileSpeciality.Monkey:
                        yield return GeneralizedLayer.Monkey;
                        break;
                    case TileSpeciality.Watermelon:
                    {
                        var layerCount = tile.GetParam(TileParamEnum.AdjacentHp);
                        if (layerCount >= 1)
                            yield return GeneralizedLayer.WatermelonOne;
                        if (layerCount >= 2)
                            yield return GeneralizedLayer.WatermelonTwo;
                        if(layerCount >= 3)
                            yield return GeneralizedLayer.WatermelonThree;
                        break;
                    }
                    case TileSpeciality.Pinata:
                        yield return GeneralizedLayer.Pinata;
                        break;
                    case TileSpeciality.Frame:
                    {
                        var layerCount = tile.GetParam(TileParamEnum.AdjacentHp);
                        if (layerCount >= 1)
                            yield return GeneralizedLayer.FrameOne;
                        if (layerCount >= 2)
                            yield return GeneralizedLayer.FrameTwo;
                        if(layerCount >= 3)
                            yield return GeneralizedLayer.FrameThree;
                        break;
                    }
                    case TileSpeciality.Hen:
                    {
                        var layerCount = tile.GetParam(TileParamEnum.AdjacentHp);
                        if (layerCount >= 1)
                            yield return GeneralizedLayer.HenOne;
                        if (layerCount >= 2)
                            yield return GeneralizedLayer.HenTwo;
                        break;
                    }
                    case TileSpeciality.Chicken:
                        yield return GeneralizedLayer.Chicken;
                        break;
                    case TileSpeciality.Hive:
                    {
                        if(tile.GetParam(TileParamEnum.BeeHiveOutOfBeesFlag) == 1)
                            yield return GeneralizedLayer.ExhaustedHive;
                        else
                        {
                            yield return GeneralizedLayer.Hive;
                        }
                        break;
                    }
                    case TileSpeciality.Bee:
                        yield return GeneralizedLayer.Bee;
                        break;
                    case TileSpeciality.Mole:
                    {
                        var layerCount = tile.GetParam(TileParamEnum.AdjacentHp);
                        if (layerCount >= 1)
                            yield return GeneralizedLayer.MoleShown;
                        if (layerCount >= 2)
                            yield return GeneralizedLayer.MoleHidden;
                        break;
                    }
                }

                if (tile.IsAnyOf(TileState.SandMod))
                    yield return GeneralizedLayer.Sand;

                if (tile.IsAnyOf(TileState.AnimalMod))
                    yield return GeneralizedLayer.Animal;

                if (tile.IsAnyOf(TileState.GameEventLabel))
                    yield return GeneralizedLayer.GameEventLabel;

                if (tile.IsAnyOf(TileState.VaseMod))
                {
                    var layerCount = tile.GetParam(TileParamEnum.VaseLayerCount);
                    if (layerCount >= 1)
                        yield return GeneralizedLayer.VaseOne;
                    if (layerCount >= 2)
                        yield return GeneralizedLayer.VaseTwo;
                    if(layerCount >= 3)
                        yield return GeneralizedLayer.VaseThree;
                    
                    // As a design rule, we always (and only) put MoneyBag under it. If that ever changes, we should
                    // account for that here
                    yield return GeneralizedLayer.MoneyBag;
                }

                if (tile.IsAnyOf(TileState.EggMod))
                {
                    var layerCount = tile.GetParam(TileParamEnum.EggLayerCount);
                    if (layerCount >= 1)
                        yield return GeneralizedLayer.EggOne;
                    if (layerCount >= 2)
                        yield return GeneralizedLayer.EggTwo;
                }
                
                if (tile.IsAnyOf(TileState.FlowerPotMod))
                {
                    var layerCount = tile.GetParam(TileParamEnum.FlowerPotLayerCount);
                    if (layerCount >= 1)
                        yield return GeneralizedLayer.FlowerPotOne;
                    if (layerCount >= 2)
                        yield return GeneralizedLayer.FlowerPotTwo;
                }

                if (tile.IsAnyOf(TileState.IceCubeMod))
                {
                    var layerCount = tile.GetParam(TileParamEnum.IceLayerCount);
                    if (layerCount >= 1)
                        yield return GeneralizedLayer.IceOne;
                    if (layerCount >= 2)
                        yield return GeneralizedLayer.IceTwo;
                    if (layerCount >= 3)
                        yield return GeneralizedLayer.IceThree;
                }

                if (tile.IsAnyOf(TileState.ChainMod))
                {
                    var layerCount = tile.GetParam(TileParamEnum.ChainLayerCount);
                    if (layerCount >= 1)
                        yield return GeneralizedLayer.ChainOne;
                    if (layerCount >= 2)
                        yield return GeneralizedLayer.ChainTwo;
                    if (layerCount >= 3)
                        yield return GeneralizedLayer.ChainThree;
                }

            }

            if (mainCell.IsAnyOf(CellState.Ivy))
                yield return GeneralizedLayer.Ivy;

        }
    }
}
