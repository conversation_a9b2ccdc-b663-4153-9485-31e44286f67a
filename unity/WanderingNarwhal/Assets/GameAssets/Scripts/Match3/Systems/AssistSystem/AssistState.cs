using System.Collections;
using System.Collections.Generic;
using BBB.CellTypes;
using GameAssets.Scripts.Match3.Logic;

namespace BBB
{
    public class AssistState : IEnumerable<KeyValuePair<long, int>>
    {
        private static Dictionary<long, int> _tempGridDict;

        private static List<long> _tempKeys;

        private readonly Dictionary<long, int> _dict = new();

        private GoalState GoalState { get; set; }

        private AssistState()
        {
        }

        public AssistState(GoalState goalState, Grid grid)
        {
            GoalState = goalState;

            foreach (var goalCountPair in goalState)
            {
                if (goalCountPair.Key.IsAssistRedefined())
                    continue;
                _dict[(long) goalCountPair.Key] = goalCountPair.Value;
            }

            foreach (var keyValueTuple in GetGridAssistState(grid))
            {
                _dict[keyValueTuple.key] = keyValueTuple.value;
            }
        }

        public IEnumerator<KeyValuePair<long, int>> GetEnumerator()
        {
            return _dict.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }

        public static IEnumerable<long> GetAssistKeys(GoalState goalState, Grid grid)
        {
            foreach (var goalCountPair in goalState)
            {
                if (goalCountPair.Key.IsAssistRedefined())
                    continue;
                
                yield return (long) goalCountPair.Key;
            }

            foreach (var keyValueTuple in GetGridAssistState(grid))
            {
                yield return keyValueTuple.key;
            }
        }
        

        private static IEnumerable<(long key, int value)> GetGridAssistState(Grid grid)
        {
            _tempGridDict ??= new Dictionary<long, int>();

            _tempGridDict.Clear();
            foreach (var cell in grid.Cells)
            {
                foreach (var (key, value) in GetCellAssistState(cell))
                {
                    _tempGridDict.TryGetValue(key, out var currentValue);
                    _tempGridDict[key] = currentValue + value;
                }
            }

            foreach (var kvp in _tempGridDict)
                yield return (kvp.Key, kvp.Value);

            _tempGridDict.Clear();
        }

        private static IEnumerable<(long key, int value)> GetCellAssistState(Cell cell)
        {
            if (cell.HasTile())
            {
                var tile = cell.Tile;

                if (tile.IsAnyOf(TileState.SquidMod | TileState.VaseMod | TileState.IceCubeMod | TileState.IceBarMod | TileState.ChainMod) ||
                    tile.Speciality is TileSpeciality.Banana or TileSpeciality.Bird or TileSpeciality.Bowling or TileSpeciality.Bush or TileSpeciality.ColorCrate or
                        TileSpeciality.Chicken or TileSpeciality.DynamiteBox or TileSpeciality.Egg or TileSpeciality.FireWorks or TileSpeciality.FlowerPot or
                        TileSpeciality.Frame or TileSpeciality.GiantPinata or TileSpeciality.GoldenScarab or TileSpeciality.Hen or TileSpeciality.IceBar or
                        TileSpeciality.JellyFish or TileSpeciality.Litter or TileSpeciality.MetalBar or TileSpeciality.MoneyBag or TileSpeciality.Monkey or
                        TileSpeciality.Pinata or TileSpeciality.Safe or TileSpeciality.Shelf or TileSpeciality.Sheep or TileSpeciality.SlotMachine or
                        TileSpeciality.Soda or TileSpeciality.Sticker or TileSpeciality.Watermelon)
                {
                    foreach (var assistState in tile.GetAssistState())
                    {
                        yield return assistState;
                    }
                }
            }
            
            foreach (var assistState in cell.GetAssistState())
            {
                yield return assistState;
            }
        }

        public void RemoveNonPositiveValues()
        {
            _tempKeys ??= new List<long>();

            lock (_tempKeys)
            {
                _tempKeys.Clear();

                foreach (var kvp in _dict)
                {
                    if (kvp.Value <= 0)
                        _tempKeys.Add(kvp.Key);
                }

                if (_tempKeys.Count > 0)
                {
                    foreach (var key in _tempKeys)
                        _dict.Remove(key);
                }

                _tempKeys.Clear();
            }
        }

        public float GetValue(long key)
        {
            if (_dict.TryGetValue(key, out var value))
            {
                return value;
            }

            return 0f;
        }

        public static AssistState operator -(AssistState first, AssistState second)
        {
            var result = new AssistState
            {
                GoalState = first.GoalState - second.GoalState
            };

            foreach (var (key, firstValue) in first._dict)
            {
                if (second._dict.TryGetValue(key, out var secondValue))
                {
                    var resultValue = firstValue - secondValue;
                    if (resultValue > 0)
                        result._dict.Add(key, resultValue);
                }
                else if (firstValue > 0)
                {
                    result._dict.Add(key, firstValue);
                }
            }

            return result;
        }
    }
}