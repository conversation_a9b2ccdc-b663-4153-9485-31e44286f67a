Checklist for adding new match3 tile type.

1. add a new Mod type to TileState enum (i.e. VaseMod).

2. add corresponding state to TileState without Mod, which will contain all gameplay affecting parameters for tile (i.e. ZeroGravity, NotMatchable, etc),

Mod is a bit-flag, which will then be passed to tiles and stored inside 32bit enum value.
When mod is assigned to tile state, it will also flip all state bits, corresponding to gameplay effects (such as ZeroGravity, etc).
This is possible thanks to mapping between mod states and non-mod states, which is implemented within the same enum and in TileStateExtensions class.

3. add mod states and corresponding full mod states to TileStateExtensions for new entries.

4. add new tile type to TileSpeciality enum.

5. add mod to GetSpecialityDefaultState method to create mapping between TileState Mod and TileSpeciality.

6. add new tile type to TileAsset enum ONLY if this tile basement-type, which is not attachable to other tiles.

For example, some tile types only exist on other tiles, such as: chains, ice, vase. They can't be basement tile, so they should not be added as tile asset.
Tile asset is used to determine base tile speciality in serialized levels and during ingame spawning.

7. add factory method to TileFactory class for new tile.

8. add TileAsset to TileSpeciality mapping in GetAssetDefaultSpeciality method.

9. optional: if new tile should be spawnable tile, then also add tileasset to the list of allowed tiles inside M3EditorSpawnerTileListItem class, 
which will enable it in the m3 editor spawner settings UI.

10. add new tile to GoalType enum.

11. add new GoalType usage in ProcessLevelStats method in M3UtilityWindow class.

12. add new tile type to ConsequenceType enum.

13. add mapping between ConsequenceType enum and GoalType enum (both ways) in AiHelper class.

14. add ConsequenceValuers for new tile in StandardHeuristic class.

15. add new tile to goal related grid check methods inside GoalState class : IsTileGridBasedGoalRelatedItem, IsTileGridBasedGoalRelatedItem, IsModStateRelatedToGoal.

This will allow goal system to automatically count tiles on grid, to determine goal progress.

16. add goal priority to GetPriority method in GoalTypeExtensions class.

17. optional: if tile should be spawnable, then add it to IsSpawnable method in GoalTypeExtensions class.

18. add TileState to GoalType mapping in method ToMod in GoalTypeExtensions class.

19. optional. if tile should be spawnable, then add it to IsSpawnedTileRelatedToGoal method in SpawnerSettingsContainer.

This will be used to check tile relation to goal before it is spawned from spawner during gameplay. 
So this allows to limit spawning for some specific tile types if corresponding goal has been reached (this is used for more flexible level design).

20. add goal sprite in GetGoalSprite in TileResourceSelector class. Also add sprite reference in the TilesResourcesExtra prefab.

21. add new tile type to TileLayerState enum.

22. add TileLayerState usage to ToSortOrder method in TileLayerStateExtensions class;

23. add TileLayerState usage to ToPrefabName method to create mapping between tile type and view prefab.

Prefab name will be used to pool and instantiate tile objects on scene to visualize tiles.

24. add TileSpeciality to TileLayerState mapping to the method 'ToLayerState' in TileLayerStateExtensions class (and same for TileState to TileLayer state mapping);

Next will be required to create 3 classes for visual representation of the tile.

25. Create tile Layer class. (i.e. VaseTileLayer etc.) in Grid/Tile/LayerHandlers directory. Layer class should contain State and IsCondition overrides for new tile.

26. Create tile LayerView class (i.e. LitterLayerView etc.) in Grid/Tile/LayerViews directory.

27. Create renderer monobehaviour class LayerRenderer (i.e. StickerLayerRenderer)

Layer and LayerView classes must be registered in Match3LevelContext class.
LayerRenderer will be attached to tile view prefab. Match3 engine will operate view via corresponding LayerView class.

28. Add new tile destroy fx type to the FxType enum.

29. Add usage of new fx type in GetGridConditionedFxTypes method.

30. Add usage of new fx type in ToFxTypes method.

31. Add mapping between fx type and prefab name in ToPrefabName method.

Prefab name then will be used by m3 engine to pool fx prefab instances before level starts.

32. Create tile prefab in TileComponentPrefabs directory.

33. Create tile destroy fx in EffectPrefabs directory.

Note: tile prefab and fx prefabs names should be same as defined in ToPrefabName methods. 
Also make sure that prefab meta-file contains correct name in addressableNames parameter of userData field,
this field is generated automatically when asset bundle is rebuilt, but before that it should be rechecked manually, 
because otherwise prefab could not be loaded properly. If you copy some other prefab to create new tile, then meta file will contain old incorrect user data.

34. Add booster applicability in method CheckApplicability in Tile class.

35. To override damage sources, add tile type usage in method GetTileStateDefaultDamageSource in TileMapper class.

36. Add button to m3 level editor scene to allow create new tile on grid. In the code there must be corresponding Add method in M3EditorTile class with special attribute [M3EditorTool("AddTILENAME")].
Note: this tool should be also registered in dictionary _toolAndOpposite in M3Editor.

After all of these steps are done, new tile should be playable on m3 levels. It can be tested in level editor test playmode.
This scenario doesn't cover more complex m3 mechanics, that can be associated with new tile, because it can be related to any part of the m3 engine, so it is unpredictable.