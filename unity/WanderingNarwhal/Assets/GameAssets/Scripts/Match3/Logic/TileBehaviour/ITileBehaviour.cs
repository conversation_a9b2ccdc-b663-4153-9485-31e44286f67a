#nullable enable
using System.Collections.Generic;
using BBB;
using BBB.Match3;
using BBB.Match3.Renderer;
using Spine.Unity;
using UnityEngine;

public interface ITileCreateBehaviour
{ 
    void Create(TileBehaviorContext tileBehaviorContext);
}

public struct TileBehaviorContext
{
    public List<TileParam>? TileParams;
    public PlayFxContext? FxContext;
    public PlaySoundContext? SoundContext;
    public PlayAnimationContext? AnimationContext;
    public PlaySpineContext? SpineContext;
}

public struct PlayFxContext
{
    public FxRenderer FxRenderer;
    public FxType FxType;
    public Coords Position;
    public float Duration;
}

public struct PlaySoundContext
{
    public string SoundId;
}

public struct PlayAnimationContext
{
    public Animator Animator;
    public int LayerIndex;
    public int UpdatedValue;
    public int AnimationTrigger;
}

public struct PlaySpineContext
{
    public SkeletonGraphic SkeletonGraphic;
    public string LayerIndexName;
    public int UpdatedValue;
}