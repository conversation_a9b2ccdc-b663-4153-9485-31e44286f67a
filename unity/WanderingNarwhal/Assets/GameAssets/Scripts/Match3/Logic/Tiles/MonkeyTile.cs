using BBB;
using BBB.Match3;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class MonkeyTile : Tile
    {
        private const int BananaSpawnCount = 1;
        private (long, int value) _assistState = ((long) GoalType.Banana, DefaultHp);

        public MonkeyTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Monkey;
            State |= TileState.Monkey;
            AddMandatoryParamsTile();
        }

        public override void AddMandatoryParamsTile()
        {
            SetParam(TileParamEnum.TileToSpawnFromReaction, (int) TileAsset.Banana);
            SetParam(TileParamEnum.TileCreateCountForReaction, BananaSpawnCount);
            base.AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.RestoresCount);
            yield return _assistState;
        }
    }
}