
namespace BBB
{
    //do not change the numbers
    public enum TileSpeciality
    {
        None            = 0,
        Sticker         = 1,
        BlinkingTile    = 2, // Tile left after the Bomb explode
        RowBreaker      = 3, // Clears row when destroyed
        ColumnBreaker   = 4, // Clears column when destroyed
        Bomb            = 5, // Clears block when destroyed
        ColorBomb       = 6, // Will clear all the tiles of a given match speciality when swapped.
        DropItem        = 7,
        Litter          = 8,
        <PERSON>nat<PERSON>          = 9,
        Sand            = 11,
        Frame           = 16,
        ColorCrate      = 17,
        Watermelon      = 18,
        MoneyBag        = 19,
        <PERSON>         = 20,
        <PERSON>             = 21,
        <PERSON>            = 22,
        <PERSON><PERSON>           = 23,
        <PERSON><PERSON>          = 24,
        <PERSON>          = 25,
        <PERSON>kunk           = 26,
        Hen             = 27,
        <PERSON>         = 28,
        <PERSON>ve            = 29,
        <PERSON>             = 30,
        <PERSON><PERSON>            = 31,
        <PERSON>quid           = 32,
        Toad            = 33,
        Propeller       = 34,
        <PERSON>         = 35,
        <PERSON>            = 36,
        Soda            = 37,
        MagicHat        = 38,
        Safe            = 39,
        FlowerPot       = 40,
        IceBar          = 41,
        DynamiteBox     = 42,
        GiantPinata     = 43,
        MetalBar        = 44,
        She<PERSON>           = 45,
        Je<PERSON><PERSON><PERSON>       = 46,
        <PERSON>Scarab    = 47,
        Gondola         = 48,
        <PERSON>kTuk          = 49,
        FireWorks       = 50,
        SlotMachine     = 51,
    }

    public static class TileSpecialityHelper
    {
        public static bool TileSpecialityComparison(TileSpeciality firstTileSpeciality,
            TileSpeciality secondTileSpeciality)
        {
            if ((firstTileSpeciality == TileSpeciality.RowBreaker ||
                 firstTileSpeciality == TileSpeciality.ColumnBreaker)
                && (secondTileSpeciality == TileSpeciality.RowBreaker ||
                    secondTileSpeciality == TileSpeciality.ColumnBreaker))
                return true;

            if (firstTileSpeciality == TileSpeciality.ColorBomb) return true;
            if (secondTileSpeciality == TileSpeciality.ColorBomb) return false;

            return firstTileSpeciality >= secondTileSpeciality;
        }

        public static int GetSpawnPriority(this TileSpeciality spec)
        {
            switch (spec)
            {
                case TileSpeciality.ColorBomb:
                    return 10;
                case TileSpeciality.Propeller:
                    return 9;
                case TileSpeciality.Bomb:
                    return 8;
                case TileSpeciality.RowBreaker:
                    return 7;
                case TileSpeciality.ColumnBreaker:
                    return 6;
                default:
                    return 0;
            }
        }

        public static bool GrassSpawnDisallowedAMod(this TileSpeciality tileSpeciality)
        {
            return tileSpeciality is TileSpeciality.Sand or TileSpeciality.Skunk or TileSpeciality.Hive
                or TileSpeciality.MagicHat or TileSpeciality.Gondola;
        }

        public static bool CanDieRepeatedlyMod(this TileSpeciality tileSpeciality)
        {
            return tileSpeciality is TileSpeciality.Monkey or TileSpeciality.Skunk or TileSpeciality.Hive
                or TileSpeciality.Squid or TileSpeciality.Toad or TileSpeciality.MagicHat or TileSpeciality.Gondola
                or TileSpeciality.TukTuk or TileSpeciality.Shelf;
        }
        
        public static bool ShouldSpawnTileAtRandomPositionMod(this TileSpeciality tileSpeciality)
        {
            return tileSpeciality is TileSpeciality.Egg or TileSpeciality.Monkey or TileSpeciality.Hen or TileSpeciality.GiantPinata
                or TileSpeciality.Hive or TileSpeciality.SlotMachine;
        }

        public static bool NoDieReactionMod(this TileSpeciality tileSpeciality)
        {
            return tileSpeciality is TileSpeciality.Sheep or TileSpeciality.Bowling or TileSpeciality.Soda
                or TileSpeciality.Safe or TileSpeciality.IceBar or TileSpeciality.DynamiteBox
                or TileSpeciality.MetalBar or TileSpeciality.JellyFish or TileSpeciality.Gondola or TileSpeciality.Skunk
                or TileSpeciality.Shelf;
        }
        
        public static bool AdjacentHitReactionMod(this TileSpeciality tileSpeciality)
        {
            return tileSpeciality.NoDieReactionMod() || tileSpeciality.CollectGoalWithAdditionalReaction();
        }

        private static bool CollectGoalWithAdditionalReaction(this TileSpeciality tileSpeciality)
        {
            return tileSpeciality is TileSpeciality.Bush or TileSpeciality.GiantPinata or TileSpeciality.SlotMachine;
        }

        public static bool ShouldSpawnSquareTiles(this TileSpeciality tileSpeciality)
        {
            return tileSpeciality is TileSpeciality.FlowerPot or TileSpeciality.Bush;
        }
        
        public static bool IsNeighbourBreaker(this TileSpeciality spec)
        {
            return spec is TileSpeciality.RowBreaker or TileSpeciality.ColumnBreaker or TileSpeciality.Bomb or TileSpeciality.Propeller;
        }

        public static bool IsFinilizable(this TileSpeciality spec)
        {
            return spec is TileSpeciality.RowBreaker or TileSpeciality.ColumnBreaker or TileSpeciality.Bomb or TileSpeciality.ColorBomb or TileSpeciality.Propeller;
        }
        
        public static bool IsRegularBoost(this TileSpeciality spec)
        {
            return spec is TileSpeciality.RowBreaker or TileSpeciality.ColumnBreaker or TileSpeciality.Bomb or TileSpeciality.Propeller;
        }

        public static bool IsBoost(this TileSpeciality spec)
        {
            return spec is TileSpeciality.RowBreaker or TileSpeciality.ColumnBreaker or TileSpeciality.Bomb or TileSpeciality.ColorBomb or TileSpeciality.Propeller;
        }
    }
}
