using System.Collections.Generic;

namespace BebopBee.Social
{
    public interface IPublicProfile
    {
        const string UidKey = "uid";
        const string AvatarKey = "avatar";
        const string NameKey = "name";
        const string DisplayNameKey = "display_name";
        const string LastUnlockedLocationIdKey = "last_unlocked_location_id";
        const string HighestPassedLevelIdKey = "highest_passed_level_id";
        const string CountryKey = "country";
        const string LastActionTimeKey = "last_action_time";
        const string AvatarFrameKey = "avatar_frame";
        const string BadgeUidKey = "badge_uid";
        const string NameStyleKey = "name_style";
        
        bool IsDirty { get; set; }
        string Uid { get; set; }
        string Avatar { get; set; }
        string FacebookAvatar { get; set; }
        string Name { get; set; }
        string DisplayName { get; set; }
        string Country { get; set; }
        string LastUnlockedLocationId { get; set; }
        string HighestPassedLevelId { get; set; }
        int LastActiveTimeStamp { get; set; }
        string AvatarFrame { get; set; }
        string BadgeUid { get; set; }
        string NameStyle { get; set; }

        Dictionary<string, object> Serialize(string userId = null);
    }
}