using BBB.BrainCloud;
using BBB.RaceEvents.UI;
using BebopBee.Social;
using GameAssets.Scripts.SocialScreens.Teams.TeamInfo;
using RPC.Social;

namespace BBB
{
    public struct AvatarInfo
    {
        public readonly string AvatarUrl;
        public readonly string Country;
        public readonly string AvatarFrameUid;
        public readonly string BadgeUid;

        // some extra customization parameters to be added later
        public AvatarInfo(string avatarUrl, string country = "", string avatarFrame = ProfileUtils.DefaultFrameUid, string badgeUid = ProfileUtils.DefaultBadgeUid)
        {
            AvatarUrl = avatarUrl;
            Country = country;
            AvatarFrameUid = avatarFrame.IsNullOrEmpty() ? ProfileUtils.DefaultFrameUid : avatarFrame;
            BadgeUid = badgeUid.IsNullOrEmpty() ? ProfileUtils.DefaultBadgeUid : badgeUid;
        }

        public AvatarInfo(LeaderboardManager.Score score)
        {
            AvatarUrl = score.Avatar;
            Country = score.Country;
            AvatarFrameUid = score.AvatarFrame.IsNullOrEmpty() ? ProfileUtils.DefaultFrameUid : score.AvatarFrame;
            BadgeUid = score.BadgeUid.IsNullOrEmpty() ? ProfileUtils.DefaultBadgeUid : score.BadgeUid;
        }

        public AvatarInfo(PublicUserInfo socialUserInfo)
        {
            AvatarUrl = socialUserInfo.Avatar;
            Country = socialUserInfo.Country;
            AvatarFrameUid = socialUserInfo.AvatarFrame.IsNullOrEmpty() ? ProfileUtils.DefaultFrameUid : socialUserInfo.AvatarFrame;
            BadgeUid = socialUserInfo.BadgeUid.IsNullOrEmpty() ? ProfileUtils.DefaultBadgeUid : socialUserInfo.BadgeUid;
        }

        public AvatarInfo(LeaderboardItem leaderboardItem)
        {
            AvatarUrl = leaderboardItem.Avatar;
            Country = string.Empty;
            AvatarFrameUid = leaderboardItem.AvatarFrame.IsNullOrEmpty() ? ProfileUtils.DefaultFrameUid : leaderboardItem.AvatarFrame;
            BadgeUid = leaderboardItem.BadgeUid.IsNullOrEmpty() ? ProfileUtils.DefaultBadgeUid : leaderboardItem.BadgeUid;
        }

        public AvatarInfo(PlayerEventLeaderboardItem playerEventLeaderboardItem)
        {
            AvatarUrl = playerEventLeaderboardItem.AvatarUrl;
            Country = playerEventLeaderboardItem.Country;
            AvatarFrameUid = playerEventLeaderboardItem.AvatarFrame.IsNullOrEmpty() ? ProfileUtils.DefaultFrameUid : playerEventLeaderboardItem.AvatarFrame;
            BadgeUid = playerEventLeaderboardItem.BadgeUid.IsNullOrEmpty() ? ProfileUtils.DefaultBadgeUid : playerEventLeaderboardItem.BadgeUid;
        }

        public AvatarInfo(PlayerRowViewData playerRowViewData)
        {
            AvatarUrl = playerRowViewData.Avatar;
            Country = playerRowViewData.Country;
            AvatarFrameUid = playerRowViewData.AvatarFrame.IsNullOrEmpty() ? ProfileUtils.DefaultFrameUid : playerRowViewData.AvatarFrame;
            BadgeUid = playerRowViewData.BadgeUid.IsNullOrEmpty() ? ProfileUtils.DefaultBadgeUid : playerRowViewData.BadgeUid;
        }

        public AvatarInfo(TeamMemberInfo teamMemberInfo)
        {
            AvatarUrl = teamMemberInfo.Avatar;
            Country = teamMemberInfo.Country;
            AvatarFrameUid = teamMemberInfo.AvatarFrame.IsNullOrEmpty() ? ProfileUtils.DefaultFrameUid : teamMemberInfo.AvatarFrame;
            BadgeUid = teamMemberInfo.BadgeUid.IsNullOrEmpty() ? ProfileUtils.DefaultBadgeUid : teamMemberInfo.BadgeUid;
        }
    }
}