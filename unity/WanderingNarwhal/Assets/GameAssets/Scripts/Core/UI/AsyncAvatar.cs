using BBB.Core;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.AvatarCustomization.UI;
using BBB.UI.Core;
using BebopBee.Social;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class AsyncAvatar : ContextedUiBehaviour
    {
        [SerializeField] private AsyncImage _avatarImage;
        [SerializeField] private string _defaultAvatar = "avatar_fennec";
        [Space]
        [SerializeField] private Image _flagImage; [Space]
        [SerializeField] private AsyncLoadablePrefab _avatarFramePrefab;
        [SerializeField] private AsyncLoadablePrefab _badgePrefab;

        private AvatarFrameView _avatarFrame;
        private FlagsLoader _flagsLoader;

        public void Setup(AvatarInfo avatarInfo)
        {
            SetupCountryAsync(avatarInfo.Country).Forget();
            SetupFrame(avatarInfo.AvatarFrameUid, avatarInfo.AvatarUrl);
            SetupBadge(avatarInfo.BadgeUid);
        }

        private async UniTask SetupCountryAsync(string country)
        {
            LazyInit();

            if (_flagImage == null || country.IsNullOrEmpty())
                return;

            var countryCode = Util.ConvertCountryNameToCountryCode(country);
            var sprite = await _flagsLoader.GetSmallFlagByCountryNameAsync(countryCode);
            _flagImage.sprite = sprite;
        }

        public void ManualStop()
        {
            if (_avatarImage != null)
            {
                _avatarImage.Stop();
            }

            if (_avatarFramePrefab != null)
            {
                if (_avatarFrame != null)
                {
                    _avatarFrame.Clear();
                    _avatarFrame = null;
                }
                _avatarFramePrefab.Clear();
            }

            if (_badgePrefab != null)
            {
                _badgePrefab.Clear();
            }
        }

        protected override void InitWithContextInternal(IContext context)
        {
            _flagsLoader = context.Resolve<FlagsLoader>();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            if (_avatarImage != null)
            {
                _avatarImage.Stop();
            }
            else
            {
                Debug.LogError($"AsyncImage is not setup for AsyncAvatar: {gameObject.name}", gameObject);
            }
            
            if (_avatarFramePrefab != null)
            {
                if (_avatarFrame != null)
                {
                    _avatarFrame.Clear();
                    _avatarFrame = null;
                }
                _avatarFramePrefab.Clear();
            }
            else
            {
                BDebug.LogError(LogCat.General,$"AsyncLoadablePrefab is not setup for Avatar Frame: {gameObject.name}");
            }

            if (_badgePrefab != null)
            {
                _badgePrefab.Clear();
            }
            else
            {
                BDebug.LogError(LogCat.General,$"AsyncLoadablePrefab is not setup for Badge: {gameObject.name}");
            }
        }
        
        private void SetupFrame(string frameUid, string avatarUrl)
        {
            LazyInit();

            if (_avatarFramePrefab == null || frameUid.IsNullOrEmpty())
                return;
           
            _avatarFramePrefab.Show(frameUid, OnFrameCreated);
            return;

            void OnFrameCreated(GameObject obj)
            {
                _avatarFrame = obj.GetComponent<AvatarFrameView>();
                if (_avatarFrame != null)
                {
                    _avatarFrame.SetupAvatar(avatarUrl);
                }
            }
        }
        
        private void SetupBadge(string badgeUid)
        {
            LazyInit();

            if (_badgePrefab == null || badgeUid.IsNullOrEmpty())
                return;
            
            _badgePrefab.Show(badgeUid);
        }

    }
}