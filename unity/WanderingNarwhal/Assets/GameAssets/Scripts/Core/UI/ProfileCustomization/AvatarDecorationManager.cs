using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using Core.Configs;
using FBConfig;

namespace BBB.AvatarCustomization.UI
{
    public sealed class AvatarDecorationManager : IAvatarDecorationManager, IContextInitializable, IContextReleasable
    {
        private const string FrameLayerUid = "Frame";
        private const string BadgeLayerUid = "Badge";  
        private static readonly Type[] RequiredConfigs =
        {
            typeof(ProfileCustomizationConfig)
        };

        private IInventory PlayerInventory => _playerManager.PlayerInventory;
        private IPlayerManager _playerManager;
        private IDictionary<string, ProfileCustomizationConfig> _profileCustomizationConfig;
        public IReadOnlyList<ProfileCustomizationConfigItem?> FramesList { get; private set; }
        public IReadOnlyList<ProfileCustomizationConfigItem?> BadgesList { get; private set; }

        public void InitializeByContext(IContext context)
        {
            Config.OnConfigUpdated -= OnConfigUpdated;
            Config.OnConfigUpdated += OnConfigUpdated;
            _playerManager = context.Resolve<IPlayerManager>();
            FramesList = new List<ProfileCustomizationConfigItem?>();
            BadgesList = new List<ProfileCustomizationConfigItem?>();
            var config = context.Resolve<IConfig>();
            OnConfigUpdated(config);
        }

        private void OnConfigUpdated(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;

            if (config == null)
            {
                BDebug.LogError(LogCat.General, "Config is null in AvatarDecorationManager");
                return;
            }
            
            _profileCustomizationConfig = config.Get<ProfileCustomizationConfig>();
            
            if(_profileCustomizationConfig == null) return;

            if (_profileCustomizationConfig.TryGetValue(FrameLayerUid, out var frameConfig))
            {
                FramesList = FlatBufferHelper.ToList(frameConfig.CustomizationsList, frameConfig.CustomizationsListLength);
            }
            
            if (_profileCustomizationConfig.TryGetValue(BadgeLayerUid, out var badgeConfig))
            {
                BadgesList = FlatBufferHelper.ToList(badgeConfig.CustomizationsList, badgeConfig.CustomizationsListLength);
            }
        }

        public bool TryAddGiftToPlayerInventory(string uid)
        {
            if (!CheckIfRewardExists(uid) || PlayerInventory.HasItem(uid)) return false;
            PlayerInventory.AddItem(uid, 1);
            return true;
        }

        private bool CheckIfRewardExists(string uid)
        {
            var frameExists = CheckIfItemExists(uid, FramesList);
            var badgeExists = CheckIfItemExists(uid, BadgesList);

            return frameExists || badgeExists;
        }

        private bool CheckIfItemExists(string uid, IReadOnlyList<ProfileCustomizationConfigItem?> listToCheck)
        {
            ProfileCustomizationConfigItem? item = null;
            foreach (var a in listToCheck)
            {
                if (a?.Uid != uid) continue;
                item = a;
                break;
            }
            return item != null;
        }

        public void ReleaseByContext(IContext context)
        {
            Config.OnConfigUpdated -= OnConfigUpdated;
        }
    }
}