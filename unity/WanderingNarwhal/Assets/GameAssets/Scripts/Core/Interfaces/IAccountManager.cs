using System;
using System.Collections.Generic;
using BBB;
using BBB.BrainCloud;
using BBB.Social.SignIn;
using BebopBee.Social;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Player;
using JetBrains.Annotations;

namespace BebopBee
{
    public interface IAccountManager
    {
        event Action<IPlayer> ProfileUpdated;
        event Action OnAvatarChanged;
        event Action TeamChanged;
        event Action TeamDataChanged;
        event Action<string> ProfileEmailUpdated;
        event Action<BCUserRaceData[]> RemoteRaceEventsDataUpdated;
        event Action<BCUserGameEventData[]> RemoteEventsDataUpdated;
        event Action<BCTriviaChallenge> TriviaChallengeUpdated;
        event Action Authenticated;

        Profile Profile { get; }
        IPlayer LocalPlayer { get; }
        bool RemoteProgressSelected { get; }
        bool IsInTeam { get; }
        [UsedImplicitly]
        bool IsAnyPlatformLoggedIn { get; }

        void LoginGrimes();
        void Logout();
        void SetTrivia(string currentTrivia, bool resetTrivia);
        void SendLoginCommand(bool applyChangesImmediately = false, Action<bool> callback = null);
        void SetTeamData(TeamData teamData, bool lastSocialModalOpening);
        void ChangeNameAndAvatar(string newAvatar, string newDisplayName, string newAvatarFrame, string newAvatarBadge, string newNameStyle);
        void HandleProfileUpdate(BCUpdatedUserProfile updatedProfileData);
        void Restart();
        void TryUpdatePlayer(Action callback, bool shouldRestart = false);
        void ValidatePlayerState(IPlayer player);
        bool TryGetPlayerProgress(IPlayer player, out PlayerProgress result);
        UniTask LocalGameSaved(PlayerSavedGame savedGame);
        void IncrementHelpCount(int amount = 1);
        void ResetHelpCount();
        void LogoutFromPlatforms();
        void LogoutFromPlatform(AccountType accountType);
        UniTask<LoginResultData> LoginToPlatform(AccountType accountType);
        List<AccountType> GetAuthSources();
        BCUserRaceData[] LastRaceEventData { get; }
        BCUserGameEventData[] LastGameEventData { get; }
        BCTriviaChallenge LastTriviaChallengeData { get; }
        void UpdateLastGameEventDataFor(string gameEventUid, Action<BCUserGameEventData> callback, Action noEventAction);
        void ResetProgression();
        void DeleteLocalPlayerData();
        void SetSdbState(bool enabled);
        void SetChallengesState(bool enabled);
        void DeleteProgression(Action onSuccess, Action onFailure);
        void UpdateEmailAndPhoneNumber(string email, string phoneNumber);
        void UpdateAppleInfo(string id = "", string familyName = "", string givenName = "", string email = "");
        void UpdateGoogleInfo(string id = "", string displayName = "", string avatar = "", string email = "");
        void UpdateFacebookInfo(string id = "", string name = "", string avatar = "", string email = "");
    }
}