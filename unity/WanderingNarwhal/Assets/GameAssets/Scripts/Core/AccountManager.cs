using System;
using System.Collections.Generic;
using System.IO;
using BBB;
using BBB.Actions;
using BBB.Core.Analytics;
using BBB.Core.Crash;
using PBGame;
using BBB.DI;
using BBB.Wallet;
using Bebopbee.Core.Systems.GamemessengerBase;
using Core.Account.Commands;
using UnityEngine;
using BBB.Controller;
using BBB.Core;
using BBB.DailyTrivia;
using Core.Configs;
using UnityEngine.Profiling;
using Debug = UnityEngine.Debug;
using BBB.MiniJSON;
using BBB.BrainCloud;
using BebopBee.Social;
using Cysharp.Threading.Tasks;
using BBB.Social.SignIn;
using FBConfig;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Database;
using GameAssets.Scripts.Database.Model;
using GameAssets.Scripts.Deeplink;
using GameAssets.Scripts.Player;
using GameAssets.Scripts.SocialScreens.Teams;
using GameAssets.Scripts.Utils;
using Realms;
using UnityEngine.SocialPlatforms;

namespace BebopBee
{
    /// <summary>
    /// Player profile manager.
    /// </summary>
    /// <remarks>
    /// the login process is executed in this steps:
    ///
    /// 1)SyncServer rpc command loads remote userid and remote pb state. this is happening at very session start, right before config is starting to download.
    /// 
    /// 2)when user presses Play button the remote state is compared to local state and selected pb state is then loaded into main player instance.
    /// 
    ///note: if comparison of local state to remote state was not match, then conflict arise and this conflict is resolved by player via SyncServer modal,
    /// but this step is not important detail and can be ignored for main login process overview (because for profile loading it doesn't matter which pb state was selected).
    /// 
    /// 3)login rpc command is called with current user_uid and facebook_id (if logined)
    ///
    /// 4)after login rpc response received the LoadCurrentSessionUserProfile rpc is called
    /// 
    /// 5)LoadCurrentSessionUserProfile rpc returns Profile for current user_uid, this profile data is then loaded into main Profile instance which is used in app.
    ///
    ///! important note ! this rpc can return not same user_uid as it was called with, but instead it can return user_uid and Profile for another user that was previously linked to same facebook_id
    ///at this point, if facebook_id was previously attached to another user_uid, that another user_uid will replace current local user_uid.
    ///On the other hand, if facebook_id was not linked to any account, then nothing will be replaced and it will be only linked to current user_uid.
    ///
    /// 5)if currently logged into Fb, we load Fb user data via FB API request and update Profile data from request results. Updated Profile automatically will be sent to game server if any field changed.
    /// -VK 
    /// </remarks>
    public class AccountManager : IAccountManager, IGameMessageListener
    {
        private static readonly string OfflineProfilePath = $"{Application.persistentDataPath}/facebook.json";
        private static bool _shouldRestartIfProfileChanged = true;
        public event Action<IPlayer> ProfileUpdated;
        public event Action OnAvatarChanged;
        public event Action TeamDataChanged;
        public event Action<string> ProfileEmailUpdated;
        public event Action<BCUserRaceData[]> RemoteRaceEventsDataUpdated;
        public event Action<BCUserGameEventData[]> RemoteEventsDataUpdated;
        public event Action<bool> ProfileProcessingStateChanged;
        public bool RemoteProgressSelected { get; private set; }
        public bool IsInTeam => Profile?.CurrentTeam != null;
        public event Action TeamChanged;
        public event Action<BCTriviaChallenge> TriviaChallengeUpdated;

        public IPlayer LocalPlayer { get; private set; }

        public Profile Profile { get; private set; }
        public bool IsAnyPlatformLoggedIn => _signInManager.IsLoggedIn();
        public BCUserRaceData[] LastRaceEventData { get; private set; }
        public BCUserGameEventData[] LastGameEventData { get; private set; }
        public BCTriviaChallenge LastTriviaChallengeData { get; private set; }

        private IPlayerLoginDataReciever _loginDataReceiver;
        private ProfileUpdater _profileUpdater;
        private bool _loggedIn;

        private SceneTransitionCommandFactory _sceneTransitionCommandFactory;
        private ScreenCommandManager _screenCommandManager;

        private IPlayerManager _playerManager;
        private IConfig _config;
        private ProtobufSerializer _protobufSerializer;
        private IEventDispatcher _dispatcher;
        private DailyTriviaServerDataHandler _dailyTriviaServerDataHandler;
        private BrainCloudManager _brainCloudManager;
        private DeepLinkExecutionManager _deepLinkExecutionManager;
        private ISignInManager _signInManager;

        private IConfigsLoader _configsLoader;

        private PlayerAuthData _serverAuthDataToUpdate;
        private PlayerGameData _serverGameDataToUpdate;
        private IRestartable _restarter;
        public event Action Authenticated;

        public async UniTask Init(IContext context)
        {
            _loginDataReceiver = context.Resolve<IPlayerLoginDataReciever>();
            _playerManager = context.Resolve<IPlayerManager>();
            _config = context.Resolve<IConfig>();
            _configsLoader = context.Resolve<IConfigsLoader>();
            _protobufSerializer = context.Resolve<ProtobufSerializer>();
            _dispatcher = context.Resolve<IEventDispatcher>();
            _dailyTriviaServerDataHandler = context.Resolve<DailyTriviaServerDataHandler>();
            _brainCloudManager = context.Resolve<BrainCloudManager>();
            _restarter = context.Resolve<IRestartable>();
            _signInManager = context.Resolve<ISignInManager>();
            _screenCommandManager = context.Resolve<ScreenCommandManager>();

            await LoadAccount();

            _profileUpdater = new ProfileUpdater(context, this);
            _profileUpdater.MonitorDataUpdated();

            _sceneTransitionCommandFactory = new SceneTransitionCommandFactory();
            _deepLinkExecutionManager = context.Resolve<DeepLinkExecutionManager>();

            Subscribe();

            _signInManager.InitContext(context);
        }

        private void Subscribe()
        {
            Unsubscribe();
            ConnectivityStatusManager.ConnectivityChanged += OnConnectionRestored;
            _dispatcher.AddListener<ForceSyncEvent>(HandleForceSyncEvent);
            _dispatcher.AddListener<SessionRenewEvent>(HandleSessionRenewEvent);
        }

        private void Unsubscribe()
        {
            ConnectivityStatusManager.ConnectivityChanged -= OnConnectionRestored;
            _dispatcher.RemoveListener<ForceSyncEvent>(HandleForceSyncEvent);
            _dispatcher.RemoveListener<SessionRenewEvent>(HandleSessionRenewEvent);
        }

        private async UniTask LoadAccount(Action postProfileCreationAction = null)
        {
            BDebug.Log(LogCat.Login, $"Loading account Uid={MultiDevice.GetUserId()}");
            Profiler.BeginSample("LocalGameSave.Load");
            var localPbPlayer = await LocalGameSave.LoadAsync(_protobufSerializer);
            Profiler.EndSample();
            Profiler.BeginSample("new Player");
            LocalPlayer = new Player(localPbPlayer);
            Profiler.EndSample();

            Profiler.BeginSample("new RedLionProfile");
            Profile = new Profile();
            await LoadOfflineInfo();
            Profiler.EndSample();

            TryMigrateChallengesState();

            postProfileCreationAction?.Invoke();
            CheckDefaultAvatar(Profile);
            CheckDisplayName(Profile);
            
            SyncPlayerWithProfile(localPbPlayer);
        }

        private void SyncPlayerWithProfile(PBPlayer pbPLayer)
        {
            // Sync install date
            if (pbPLayer.InstallDate > 0)
            {
                Profile.InstallDate = pbPLayer.InstallDate;
            }
        
            Profile.FirstTryWins = pbPLayer.Stats?.FirstTryWinsCount ?? 0;
            Profile.HelpsReceived = pbPLayer.Stats?.TotalHelpsReceivedCount ?? 0;
            Profile.LeaguesWon = pbPLayer.Stats?.LeaguesWon ?? 0;
        }

        private void HandleForceSyncEvent(ForceSyncEvent obj)
        {
            CrashLoggerService.Log("Handling Force Sync Event");
            _configsLoader.SendLoadConfigsCommand();
            SendLoginCommand();
        }

        private void HandleSessionRenewEvent(SessionRenewEvent obj)
        {
            CrashLoggerService.Log("Handling Session Renew Event");
            _configsLoader.SendLoadConfigsCommand();
            SendLoginCommand();
        }

        public void LoginGrimes()
        {
            ReportToAnalytics();
            _loginDataReceiver.ScenePreInstantiate();
            _loggedIn = true;
        }

        private bool HasAnyTaskProgress(PBPlayer pbPlayer)
        {
            if (pbPlayer.EpisodeScenesProgress.IsNullOrEmpty())
                return false;

            foreach (var (_, progress) in pbPlayer.EpisodeScenesProgress)
            {
                if (progress.CompletedTasks is { Count: > 0 })
                    return true;
            }

            return false;
        }

        private async UniTask ValidateLevelProgress()
        {
            await UniTask.SwitchToMainThread();
            _profileUpdater.RefreshHighestPassedLevel();
        }

        public void ValidatePlayerState(IPlayer player)
        {
            var pbPlayer = player.PlayerDO;

            if (pbPlayer == null)
                return;

            if (pbPlayer.LevelStates == null || pbPlayer.LevelStates.Count == 0)
                return;

            var previousStage = 1;
            foreach (var levelState in pbPlayer.LevelStates)
            {
                if (levelState.Stage > 0 && previousStage == 0)
                {
                    BDebug.LogError(LogCat.Player,
                        $"Invalid level stage for {levelState.SourceUid} level, it was completed in out of order");
                    levelState.Stage = 0; //reset level stage, it was completed before the previous level
                }

                previousStage = levelState.Stage;
            }
        }

        public bool TryGetPlayerProgress(IPlayer player, out PlayerProgress result)
        {
            result = new PlayerProgress();
            var pbPlayer = player.PlayerDO;

            if (pbPlayer == null)
                return false;

            if (pbPlayer.LevelStates == null || pbPlayer.LevelStates.Count == 0)
                return false;
            foreach (var levelState in pbPlayer.LevelStates)
            {
                if (levelState.Stage > 0)
                {
                    result.MaxLevel++;
                }
            }

            result.MaxLevel += pbPlayer.RemovedLevelStatesIdsList.Count;

            return true;
        }

        /// <summary>
        /// Updates the trivia to show in this session
        /// </summary>
        /// <param name="currentTrivia">Uid of trivia from DailyTriviaConfig</param>
        /// <param name="resetTrivia">If True - resets already shown trivias, required to cycle trivias</param>
        public void SetTrivia(string currentTrivia, bool resetTrivia)
        {
            _dailyTriviaServerDataHandler.SetCurrentTriviaUidFromServer(currentTrivia, resetTrivia);
        }

        private async UniTask SelectRemoteStorage(IPlayer remotePlayer)
        {
            if (remotePlayer == null) return;

            LocalPlayer = remotePlayer;
            var bytes = _protobufSerializer.SerializeToBytes<PBPlayer, GameSerializer>(remotePlayer.PlayerDO);
            await LocalGameSave.TryStoreAsync(bytes);
        }

        public void UpdateLastGameEventDataFor(string eventId, Action<BCUserGameEventData> callback, Action noEventCallback)
        {
            _brainCloudManager.GetSingleEventRemoteData(eventId, response =>
            {
                var responseData = response.Data.Response;
                if (responseData.GameEventData != null && LastGameEventData != null)
                {
                    for (var i = 0; i < LastGameEventData.Length; i++)
                    {
                        if (LastGameEventData[i].EventId != responseData.GameEventData.EventId) continue;

                        LastGameEventData[i] = responseData.GameEventData;
                        callback?.Invoke(responseData.GameEventData);
                        break;
                    }
                }
                else
                {
                    noEventCallback?.Invoke();
                }
            }, () => { BDebug.LogError(LogCat.General, $"Failed to fetch data for game event"); });
        }

        public void DeleteProgression(Action onSuccess, Action onFailure)
        {
            ProfileProcessingStateChanged?.Invoke(true);
            _brainCloudManager.DeleteProgression(OnSuccess, OnFailure);
            return;

            void OnSuccess()
            {
                onSuccess?.Invoke();
                ResetPlayer.ResetLocalPlayer();
                _brainCloudManager.Logout();
                _signInManager.Reset();
                _restarter.Restart(true);
            }

            void OnFailure()
            {
                onFailure?.Invoke();
                ProfileProcessingStateChanged?.Invoke(false);
            }
        }

        public void ResetProgression()
        {
            ProfileProcessingStateChanged?.Invoke(true);
            _brainCloudManager.ResetProgression(OnSuccess, OnFailure);
            return;

            void OnSuccess()
            {
                ResetPlayer.ResetLocalPlayer();
                _brainCloudManager.Logout();
                _restarter.Restart(true);
            }

            void OnFailure()
            {
                ProfileProcessingStateChanged?.Invoke(false);
            }
        }

        public void SendLoginCommand(bool applyChangesImmediately = false, Action<bool> callback = null)
        {
            if (Profile.Uid.IsNullOrEmpty())
            {
                // Uid can be empty, theoretically, if used debug delete user and then start login without app restart.
                CrashLoggerService.Log("User uid is empty before start Login command");
                MultiDevice.GetUserId();
            }

            ValidatePlayerState(LocalPlayer);
            TryGetPlayerProgress(LocalPlayer, out var playerProgress);
            var authUserId = _signInManager.GetLinkedUserId();
            _brainCloudManager.RequestAuthentication(Profile, playerProgress, authUserId, SuccessCallback, FailureCallback);
            return;

            void FailureCallback()
            {
                callback.SafeInvoke(false);

                ConnectivityStatusManager.ConnectivityChanged -= RetryAction;
                ConnectivityStatusManager.ConnectivityChanged += RetryAction;
                return;

                void RetryAction(bool reachable)
                {
                    if (!reachable) return;
                    ConnectivityStatusManager.ConnectivityChanged -= RetryAction;
                    SendLoginCommand(applyChangesImmediately, callback);
                }
            }

            void SuccessCallback(BCAuthResponse response)
            {
                var shouldRestart = _shouldRestartIfProfileChanged && !MultiDevice.GetOldUserId().IsNullOrEmpty();
                var authData = response.Data;
                _config.UpdateBrainCloudConfigOverrides(authData.GlobalProperties);
                _serverAuthDataToUpdate = new PlayerAuthData(authData);
                _serverGameDataToUpdate = new PlayerGameData(authData.GameData, authData.AdminGifts, authData.AdminGiftData, authData.VipCoins, authData.VipClaimedLocations);
                UpdateLocalPlayerWithServerData(applyChangesImmediately, shouldRestart, () => callback.SafeInvoke(true));
            }
        }

        private void UpdateTeamWithServerData(BCTeamData[] teamData)
        {
            if (teamData is { Length: > 0 })
            {
                for (var i = teamData.Length - 1; i >= 0; i--)
                {
                    var team = teamData[i];

                    if (team?.groupMembers == null) continue;

                    foreach (var member in team.groupMembers)
                    {
                        if (member.Value.summaryFriendData.Uid != _brainCloudManager.ProfileId &&
                            member.Value.summaryFriendData.Uid != Profile.Uid) continue;

                        SetTeamData(team.ToTeamData(), false);
                        break;
                    }
                }

                if (teamData.Length > 1)
                {
                    Debug.LogError($"[DEBUG ACCOUNT ONLY] Found more than one team channels");
                }
            }
            else
            {
                SetTeamData(null, false);
            }
        }

        public void SetTeamData(TeamData teamData, bool lastSocialModalOpening)
        {
            var isTeamChanged = (teamData != null) ^ (Profile.CurrentTeam != null);
            if (!isTeamChanged && teamData != null && Profile.CurrentTeam != null)
            {
                isTeamChanged = teamData.TeamUid != Profile.CurrentTeam.TeamUid;
            }

            var kickedFromTeam = false;

            if (teamData != null && !isTeamChanged)
            {
                kickedFromTeam = true;
                foreach (var member in teamData.Members)
                {
                    if (member.Uid != _brainCloudManager.ProfileId && member.Uid != Profile.Uid) continue;

                    kickedFromTeam = false;
                    break;
                }
            }

            if (Profile.CurrentTeam?.JoinedAt > 0 && teamData != null && !kickedFromTeam)
            {
                teamData.JoinedAt = Profile.CurrentTeam.JoinedAt;
            }

            var prevTeamData = Profile?.CurrentTeam;

            if (kickedFromTeam)
            {
                BDebug.Log(LogCat.Social, $"[AccountManager] Can't find {_brainCloudManager.ProfileId} in {teamData.TeamUid} team");
                if (Profile != null)
                {
                    Profile.CurrentTeam = null;
                }

                isTeamChanged = true;
            }
            else if (Profile != null)
            {
                var currentTeam = Profile.CurrentTeam;

                if (!isTeamChanged && currentTeam != null && teamData != null &&
                    (currentTeam.Name != teamData.Name || currentTeam.Icon != teamData.Icon))
                {
                    TeamDataChanged.SafeInvoke();
                }

                Profile.CurrentTeam = teamData;
            }

            if (!isTeamChanged) return;

            if (prevTeamData != null)
            {
                //this caching ensures the data will be transient from session to session
                _playerManager?.Player?.TeamDataCacheProxy?.UpdatePrevTeamData(prevTeamData);
            }

            if (Profile is { CurrentTeam: not null })
            {
                var prevTeamDataCache = _playerManager?.Player?.TeamDataCacheProxy?.GetPrevTeamData();
                Analytics.LogEvent(new TeamChangedEvent(Profile.CurrentTeam, prevTeamDataCache, lastSocialModalOpening));
            }
            else
            {
                Analytics.LogEvent(new TeamExitEvent(prevTeamData));
            }

            TeamChanged?.Invoke();
        }

        public async void TryUpdatePlayer(Action callback = null, bool shouldRestart = false)
        {
            RemoteProgressSelected = false;
            TryProcessPlayerAuthenticationData();
            await TryProcessPlayerGameData();
            callback.SafeInvoke();
            if (shouldRestart)
            {
                RestartGame();
            }
        }

        private void TryProcessPlayerAuthenticationData()
        {
            if (_serverAuthDataToUpdate == null) return;

            var serverAuthDataToUpdate = _serverAuthDataToUpdate;
            _serverAuthDataToUpdate = null;

            _shouldRestartIfProfileChanged = false;
            MultiDevice.ResetOldUserId();
            Profile.UpdateFromBrainCloudResponse(serverAuthDataToUpdate);

            LastRaceEventData = serverAuthDataToUpdate.RaceEventData;
            RemoteRaceEventsDataUpdated.SafeInvoke(LastRaceEventData);
            LastGameEventData = serverAuthDataToUpdate.GameEventData;
            RemoteEventsDataUpdated.SafeInvoke(LastGameEventData);
            LastTriviaChallengeData = serverAuthDataToUpdate.TriviaChallengeData;
            TriviaChallengeUpdated.SafeInvoke(LastTriviaChallengeData);

            UpdateTeamWithServerData(serverAuthDataToUpdate.TeamData);
            Authenticated?.Invoke();
        }

        private async UniTask TryProcessPlayerGameData()
        {
            if (_serverGameDataToUpdate == null) return;

            var serverGameDataToUpdate = _serverGameDataToUpdate;
            _serverGameDataToUpdate = null;

            var adminGiftData = serverGameDataToUpdate.AdminGiftData; 
            _deepLinkExecutionManager.SetAdminGifts(serverGameDataToUpdate.AdminGifts, adminGiftData?.Title, adminGiftData?.Message, adminGiftData?.RewardTitle);

            if (serverGameDataToUpdate.GameData.IsNullOrEmpty())
            {
                UpdateVipCoins(LocalPlayer.PlayerDO, serverGameDataToUpdate.VipCoins, serverGameDataToUpdate.VipClaimedLocations);
                await ValidateLevelProgress();
                Profile.UpdateTrophies(LocalPlayer);
                return;
            }

            Profiler.BeginSample("ParsePlayerGameDataOnSuccess - _onScreenChange");
            var pbPlayer = _protobufSerializer.Decompress(Convert.FromBase64String(serverGameDataToUpdate.GameData));
            var remotePbPlayer = _protobufSerializer.DeserializeBytes<PBPlayer, GameSerializer>(pbPlayer);
            UpdateVipCoins(remotePbPlayer, serverGameDataToUpdate.VipCoins, serverGameDataToUpdate.VipClaimedLocations);
            var remotePlayer = new Player(remotePbPlayer);
            ValidatePlayerState(remotePlayer);
            await SelectRemoteStorage(remotePlayer);
            TryMigrateChallengesState();
            ProfileUpdated.SafeInvoke(LocalPlayer);
            SyncPlayerWithProfile(remotePbPlayer);
            Profiler.EndSample();
        }

        private void TryMigrateChallengesState()
        {
            if (!LocalPlayer.SdbMigrated) return;

            // First time after sdb/wiggles decoupling, we need to migrate ChallengesEnabled from SdbEnabled
            SetChallengesState(Profile.SdbEnabled);
        }

        private void ReportToAnalytics()
        {
            Analytics.LogEvent(new LoginEvent(GetAuthSources(),
                Profile.FacebookId,
                Profile.FacebookName,
                Profile.AppleId,
                Profile.AppleGivenName,
                Profile.GoogleId,
                Profile.GoogleDisplayName));
        }

        public List<AccountType> GetAuthSources()
        {
            return _signInManager.GetAuthSources();
        }

        /**
         * Logout is called on app restart
         */
        public void Logout()
        {
            Unsubscribe();
            _loggedIn = false;
            _serverGameDataToUpdate = null;
            _serverAuthDataToUpdate = null;
        }

        private void UpdateLocalPlayerWithServerData(bool applyChangesImmediately = false, bool shouldRestart = false, Action callback = null)
        {
            RemoteProgressSelected = true;
            var noGameData = _serverGameDataToUpdate == null || _serverGameDataToUpdate.GameData.IsNullOrEmpty();
            if (noGameData || applyChangesImmediately || shouldRestart)
            {
                TryUpdatePlayer(callback, shouldRestart);
                return;
            }

            callback.SafeInvoke();
        }

        private static void UpdateVipCoins(PBPlayer pbPlayer, int vipCoins, List<string> vipClaimedLocations)
        {
            if (vipCoins > 0)
            {
                var wallet = pbPlayer.Wallet;
                if (wallet.WalletCurrency != null)
                {
                    var currencyDict = wallet.WalletCurrency;
                    if (currencyDict.TryGetValue(WalletCurrencies.VipCurrency, out var vipWallet))
                    {
                        vipWallet.Spent = 0;
                        vipWallet.Bought = 0;
                        vipWallet.Earned = vipCoins;
                    }
                    else
                    {
                        currencyDict[WalletCurrencies.VipCurrency] = new PBWalletCurrency
                        {
                            Earned = vipCoins
                        };
                    }
                }
            }

            if (vipClaimedLocations.IsNullOrEmpty()) return;
            pbPlayer.ClaimedLocations ??= new List<string>();
            foreach (var location in vipClaimedLocations)
            {
                if (pbPlayer.ClaimedLocations.Contains(location))
                    continue;

                pbPlayer.ClaimedLocations.Add(location);
            }
        }

        private void LoginPlatformAndRestart(Action callback = null)
        {
#if BBB_DEBUG
            if (OpenExternal._debugIsExtrnalUrlOpenDisabled)
            {
                return;
            }
#endif
            var commandQueue = _sceneTransitionCommandFactory.LoginAndRestartCommand();

            commandQueue.CommandDoneEvent -= CommandDoneHandler;
            commandQueue.CommandDoneEvent += CommandDoneHandler;

            _screenCommandManager.PushCmd(commandQueue);
            return;

            void CommandDoneHandler(CommandStatus status, CommandCode code)
            {
                if (status == CommandStatus.Success)
                {
                    callback?.Invoke();
                }

                commandQueue.CommandDoneEvent -= CommandDoneHandler;
            }
        }

        private void RestartGame()
        {
            _screenCommandManager.PushCmd(new RestartGameCommand());
        }

        private void SaveProfileData(ProfileDataModel profileDataModel)
        {
            DbManager.SaveData(profileDataModel);
            Profile.ProfileDataModel ??= DbManager.LoadData<ProfileDataModel>();
        }

        private async UniTask LoadOfflineInfo()
        {
            var userId = MultiDevice.GetUserId();
            var newName = GenerateName();
            Profile.ProfileDataModel = DbManager.LoadData<ProfileDataModel>();
            if (Profile.ProfileDataModel == null)
            {
                if (!File.Exists(OfflineProfilePath))
                {
                    SaveProfileData(new ProfileDataModel(userId, newName));
                    return;
                }

                var playerProfile = await File.ReadAllTextAsync(OfflineProfilePath);

                if (Json.Deserialize(playerProfile) is Dictionary<string, object> profile)
                {
                    SaveProfileData(new ProfileDataModel(profile));
                }
                else
                {
                    SaveProfileData(new ProfileDataModel(userId, newName));
                    BDebug.LogError(LogCat.Player, $"Failed to deserialize offline profile: {playerProfile}");
                }

                File.Delete(OfflineProfilePath);
            }

            if (Profile.Uid.IsNullOrEmpty())
            {
                // Failsafe just in case serialized offline profile file contains no uid;
                Profile.Uid = userId;
                BDebug.Log(LogCat.General, $"[Braincloud] After Profile.Uid:{Profile.Uid}");
            }

            if (Profile.DisplayName.IsNullOrEmpty())
            {
                Profile.DisplayName = newName;
            }
        }

        private void CheckDisplayName(Profile profile)
        {
            var displayNameConfig = _config.TryGetDefaultFromDictionary<FBConfig.DefaultNamesConfig>();

            if (displayNameConfig.FirstWordLength == 0)
            {
                Debug.LogError("DefaultNamesConfig First Word column is missing, display name will not be generated");
                return;
            }

            if (displayNameConfig.SecondWordLength == 0)
            {
                Debug.LogError("DefaultNamesConfig Second Word column is missing, display name will not be generated");
                return;
            }

            if (!profile.DisplayName.IsNullOrEmpty()) return;

            var generatedName = GenerateName();

            profile.ChangeDisplayName(generatedName);
        }

        private string GenerateName()
        {
            var displayNameConfig = _config.TryGetDefaultFromDictionary<FBConfig.DefaultNamesConfig>();
            return displayNameConfig.FirstWord(UnityEngine.Random.Range(0, displayNameConfig.FirstWordLength)) +
                   displayNameConfig.SecondWord(UnityEngine.Random.Range(0, displayNameConfig.SecondWordLength));
        }

        public void ChangeNameAndAvatar(string newAvatar, string newDisplayName, string newAvatarFrame, string newAvatarBadge, string newNameStyle)
        {
            var oldAvatar = Profile.Avatar;
            var oldName = Profile.DisplayName;
            var oldFrame = Profile.AvatarFrame;
            var oldBadge = Profile.BadgeUid;
            var oldNameStlye = Profile.NameStyle;
            
            if (newAvatar.IsNullOrEmpty())
            {
                newAvatar = oldAvatar;
            }

            if (newDisplayName.IsNullOrEmpty())
            {
                newDisplayName = oldName;
            }

            if (newAvatar.IsNullOrEmpty())
            {
                newAvatarFrame = oldFrame;
            }

            if (newAvatarBadge.IsNullOrEmpty())
            {
                newAvatarBadge = oldBadge;
            }
            
            if (newNameStyle.IsNullOrEmpty())
            {
                newNameStyle = oldNameStlye;
            }

            Profile.ChangeNameAndAvatar(newAvatar, newDisplayName, newAvatarFrame, newAvatarBadge, newNameStyle);

            if (newAvatar != oldAvatar)
            {
                OnAvatarChanged.SafeInvoke();
            }
            
            if (newAvatarFrame != oldFrame || newAvatarBadge != oldBadge)
            {
                OnAvatarChanged.SafeInvoke();
            }
            
            // if (newNameStyle != oldNameStlye)
            // {
            //     OnNameStyleChanged.SafeInvoke();
            // }
            
            if (newDisplayName != oldName)
            {
                _brainCloudManager.SaveProgress(PlayerProgress.Default, Profile, null, _ => { }, () => { });
            }
        }

        public void HandleProfileUpdate(BCUpdatedUserProfile updatedProfileData)
        {
            LastTriviaChallengeData = updatedProfileData.TriviaChallenge;
            TriviaChallengeUpdated.SafeInvoke(LastTriviaChallengeData);
            Profile.IsDirty = false;
        }

        public void Restart()
        {
            CrashLoggerService.Log("AccountManager.Restart");
            BDebug.Log(LogCat.Profile, "AccountManager.Restart");

            Unsubscribe();

            LastGameEventData = null;
            LastRaceEventData = null;
            LastTriviaChallengeData = null;

            Profile.ProfileDataModel = null;

            _profileUpdater?.Destroy();
        }

        private void OnConnectionRestored(bool reachable)
        {
            if (reachable)
            {
            }
        }

        private static void CheckDefaultAvatar(Profile profile)
        {
            if (profile.Avatar.IsNullOrEmpty() || profile.Avatar == GameConstants.OldAutoAvatar)
            {
                profile.Avatar = GenericResourceProvider.DefaultAvatars.GetRandomItem();
            }
        }

        public bool IsLoggedIn()
        {
            return _loggedIn;
        }

        public void OnMessage(IGameMessage message)
        {
            if (message is AccountManagerMessage result)
            {
                result.Apply(this);
            }
        }

        public async UniTask<LoginResultData> LoginToPlatform(AccountType accountType)
        {
            BDebug.Log(LogCat.Login, $"Trying to login to {accountType}");
            ProfileProcessingStateChanged?.Invoke(true);
            var result = await _signInManager.LogInAsync(accountType);
            BDebug.Log(LogCat.Login, $"Login {accountType} result - success: {result}.");
            if (result.Cancelled)
            {
                UserLoginCancel();
                return result;
            }

            if (!result.Success)
            {
                UserLoginError(result.Error);
                return result;
            }

            UserDidLogin().Forget();
            return result;
        }

        private async UniTask UserDidLogin()
        {
            await UniTask.SwitchToMainThread();
            CrashLoggerService.Log("AccountManager.UserDidLogin");

            var oldId = Profile.Uid;
            var authUserId = _signInManager.GetLinkedUserId();
            var newId = !authUserId.IsNullOrEmpty() ? authUserId : oldId;
            BDebug.Log(LogCat.Login,
                $"AccountManager.UserDidLogin profileId: {oldId}, linkedUserId: {authUserId}, signedUserId: {_signInManager.CurrentUserId()}, multiDeviceUserId: {MultiDevice.GetUserId()}");

            _shouldRestartIfProfileChanged = false;
            MultiDevice.SetOldUserId(oldId);
            MultiDevice.SetUserId(newId);
            UpdateSignedInfo(_signInManager.CurrentDisplayName(), _signInManager.CurrentAvatar(), _signInManager.CurrentEmail());
            if (!oldId.IsNullOrEmpty() && !newId.IsNullOrEmpty() && oldId.Equals(newId) && Profile.IsDirty)
            {
                _brainCloudManager.SaveProgress(PlayerProgress.Default, Profile, null, (_) => LoginPlatformAndRestart(), () => LoginPlatformAndRestart());
                return;
            }

            LoginPlatformAndRestart();
        }

        private async UniTask UserDidLogout()
        {
            await UniTask.SwitchToMainThread();
            CrashLoggerService.Log("AccountManager.UserDidLogout");
            BDebug.Log(LogCat.Login, $"AccountManager.UserDidLogout profileId: {Profile.Uid}, signedUserId: {_signInManager.CurrentUserId()}, multiDeviceUserId: {MultiDevice.GetUserId()}");

            MultiDevice.ResetPlayer();
            ResetPlayer.ResetLocalPlayer();
            _brainCloudManager.Logout();

            BDebug.Log(LogCat.General, $"[Reset] ResetPlayer.ShouldNotRestart:{ResetPlayer.ShouldNotRestart}");

            // User requested to delete its player?
            if (ResetPlayer.ShouldNotRestart)
            {
                ProfileProcessingStateChanged?.Invoke(false);
                return;
            }

            await LoadAccount();
            LoginPlatformAndRestart();
        }

        private void UserLoginError(string arg)
        {
            BDebug.LogError(LogCat.Login, $"Login FAILED arg={arg}");
            ResetLoginState();
        }

        private void UserLoginCancel()
        {
            BDebug.Log(LogCat.Login, $"Login CANCELED");
            ResetLoginState();
        }

        private void ResetLoginState()
        {
            ProfileProcessingStateChanged?.Invoke(false);
        }

        public async void LogoutFromPlatforms()
        {
            BDebug.Log(LogCat.Login, $"Logout from all platforms");
            ProfileProcessingStateChanged?.Invoke(true);
            var error = _signInManager.LogOut();
            BDebug.Log($"Logout result - {(error.IsNullOrEmpty() ? "Success" : $"Error: {error}")}");
            if (!error.IsNullOrEmpty())
            {
                UserLoginError(error);
                return;
            }

            CrashLoggerService.Log("AccountManager: User did logout");
            BDebug.Log(LogCat.Login, $"AccountManager: User did logout userId: {MultiDevice.GetUserId()}");

            await _signInManager.TrySignInAnonymouslyAsync();

            UserDidLogout().Forget();
        }

        public void LogoutFromPlatform(AccountType accountType)
        {
            BDebug.Log(LogCat.Login, $"Trying to logout from {accountType}.");
            _signInManager.LogOutPlatform(accountType);
        }

        public async UniTask LocalGameSaved(PlayerSavedGame savedGame)
        {
            if (!RemoteProgressSelected)
            {
                LocalGameSave.MarkRemoteSavingInProgress(true);

                var pbBytes = savedGame.Bytes;
                var pbBase64 = await UniTask.RunOnThreadPool(() => Convert.ToBase64String(_protobufSerializer.Compress(pbBytes)));

                if (savedGame.CouldGetProgress)
                {
                    void SuccessCallback(BCSaveProgressResponse response)
                    {
                        LocalGameSave.MarkRemoteSavingInProgress(false);

                        // Check if the server returned some game data. If so, it means that the game in the server has more progress than
                        // what we have locally, so we need to update our local game. We'll do this later, on screen transition.
                        
                        var saveGameResponse = response.Response.SaveGame;
                        if (saveGameResponse != null)
                        {
                            _serverGameDataToUpdate = new PlayerGameData(saveGameResponse.GameData, saveGameResponse.AdminGifts, saveGameResponse.AdminGiftData, saveGameResponse.VipCoins, saveGameResponse.VipClaimedLocations);
                        }

                        UpdateLocalPlayerWithServerData();
                        // If UpdateProfile is null/empty we don't need to sync anything
                        if (response.Response.UpdateProfile != null)
                        {
                            HandleProfileUpdate(response.Response.UpdateProfile);
                        }
                    }

                    void FailureCallback()
                    {
                        LocalGameSave.MarkRemoteSavingInProgress(false);
                    }

                    // TODO: Handle force sync up?
                    _brainCloudManager.SaveProgress(savedGame.PlayerProgress, Profile, pbBase64, SuccessCallback, FailureCallback);
                }
            }
        }

        public void IncrementHelpCount(int amount = 1)
        {
            LocalPlayer.PlayerDO.HelpCount += amount;
            LocalPlayer.PlayerDO.Stats.TotalHelpsMadeCount += amount;
            Profile.HelpCount = LocalPlayer.PlayerDO.HelpCount;
        }

        public void ResetHelpCount()
        {
            LocalPlayer.PlayerDO.HelpCount = 0;
            Profile.HelpCount = LocalPlayer.PlayerDO.HelpCount;
        }

        public void SetSdbState(bool enabled)
        {
            Profile.SdbEnabled |= enabled;
        }

        public void SetChallengesState(bool enabled)
        {
            Profile.ChallengesEnabled |= enabled;
        }

        public void DeleteLocalPlayerData()
        {
            var profile = Profile.ProfileDataModel;
            Profile.ProfileDataModel = profile.Freeze();
            DbManager.DropCollection(profile);
            Profile.ProfileDataModel = null;
            _signInManager.Reset();
        }

        private void InvokeProfileEmailUpdated() => ProfileEmailUpdated.SafeInvoke(Profile.Email);

        public void UpdateEmailAndPhoneNumber(string email, string phoneNumber)
        {
            Profile.UpdateEmailAndPhoneNumber(email, phoneNumber);
            InvokeProfileEmailUpdated();
        }

        public void UpdateAppleInfo(string id = "", string familyName = "", string givenName = "", string email = "")
        {
            Profile.UpdateAppleInfo(id, familyName, givenName, email);
            InvokeProfileEmailUpdated();
        }

        public void UpdateGoogleInfo(string id = "", string displayName = "", string avatar = "", string email = "")
        {
            Profile.UpdateGoogleInfo(id, displayName, avatar, email);
            InvokeProfileEmailUpdated();
        }

        public void UpdateFacebookInfo(string id = "", string name = "", string avatar = "", string email = "")
        {
            Profile.UpdateFacebookInfo(id, name, avatar, email);
            InvokeProfileEmailUpdated();
        }

        public void UpdateSignedInfo(string name, string avatar, string email)
        {
            Profile.UpdateSignedInfo(name, avatar, email);
            InvokeProfileEmailUpdated();
        }
    }
}