using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.DI;
using Core.Configs;
using FBConfig;

namespace BebopBee.Social
{
    public class FakeUsersManager : IContextInitializable
    {
        private static readonly Type[] RequiredConfigs = {
            typeof(ProgressionLevelConfig),
            typeof(FakeUsersConfig)
        };

        private const string NonLocalizedPrefix = "NL_";

        private ILocalizationManager _localizationManager;
        private readonly IDictionary<string, FakeUsersConfig> _fakeUsersConfigDict = new Dictionary<string, FakeUsersConfig>();
        
        private readonly List<FakeUsersConfig> _fakeUsersConfigList = new ();

        public void InitializeByContext(IContext context)
        {
            _localizationManager = context.Resolve<ILocalizationManager>();

            var config = context.Resolve<IConfig>();
            Init(config);
            Config.OnConfigUpdated -= Init;
            Config.OnConfigUpdated += Init;
        }

        private void Init(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;
            
            _fakeUsersConfigDict.Clear();
            _fakeUsersConfigList.Clear();
            var configDict = config.Get<FakeUsersConfig>();
            
            if (configDict == null) return;
            
            foreach (var kv in configDict)
            {
                // Fake users list in server config can never be empty.
                // If we want to remove all NPCs, then we just leave one NPC in the list and clear avatar url for it.
                if (kv.Value.AvatarUrl.IsNullOrEmpty() || kv.Value.NameLoc.IsNullOrEmpty()) continue;
                
                _fakeUsersConfigDict[kv.Key] = kv.Value;
                _fakeUsersConfigList.Add(kv.Value);
            }
        }

        public IEnumerable<UserPublicProfile> GetFakeUsersPublicProfiles()
        {
            if (_fakeUsersConfigDict == null)
            {
                BDebug.LogError(LogCat.Social, "Fake users config dict is null");
                yield break;
            }

            foreach (var conf in _fakeUsersConfigDict.Values)
            {
                var data = new Dictionary<string, object>()
                {
                    { IPublicProfile.AvatarKey, conf.AvatarUrl },
                    { IPublicProfile.UidKey, conf.Uid },
                    { IPublicProfile.NameKey, GetName(conf.NameLoc) },
                    { IPublicProfile.LastUnlockedLocationIdKey, conf.LastUnlockedLocationId },
                    { IPublicProfile.HighestPassedLevelIdKey, conf.HighestPassedLevelId },
                    { IPublicProfile.AvatarFrameKey, ProfileUtils.DefaultFrameUid },
                };

                yield return new UserPublicProfile(data);
            }
        }

        private string GetName(string nameLoc)
        {
            if (string.IsNullOrEmpty(nameLoc))
                return nameLoc;

            return nameLoc.StartsWith(NonLocalizedPrefix) ? nameLoc.Replace(NonLocalizedPrefix, string.Empty) : _localizationManager.getLocalizedText(nameLoc);
        }
    }
}