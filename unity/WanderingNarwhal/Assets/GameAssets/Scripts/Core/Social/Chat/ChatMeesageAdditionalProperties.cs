namespace BBB.Chat
{
    public static class ChatMessageAdditionalProperties
    {
        public const string UserCountry = "country";

        public const string VideoOrientation = "orientation";
        public const string ImageOrientation = "orientation";
        
        // IAP Shared message
        public const string ProductID = "product_id";
        
        // Admin rights changed message
        public const string AdminName = "admin_name";
        public const string IsAdmin = "is_admin";
        
        // team edit message properties
        public const string TeamNewName = "new_name";
        public const string TeamNewDescription = "new_desc";
        
        public const string TeamEventUid = "team_event_uid";
        public const string TeamVsTeamRecipientUid = "receiver_uid";
        
        public const string IceBreakerQuestionUid = "ice_breaker_question_uid";
    }
}