using System;
using System.Collections.Generic;
using BBB.BrainCloud;
using GameAssets.Scripts.SocialScreens.Teams.TeamChat.Messages;

namespace BBB.Social.Chat
{
    public interface IChatManager
    {
        public event Action KickedFromChat;
        event Action ConnectedToChatSuccess;
        event Action<bool> ConnectedToChatFailure;
        event Action<ChatMessage> ChatMessageAdded;
        event Action<ChatMessage> ChatMessageUpdated;
        event Action<ChatMessage> ChatMessageDeleted;
        event Action<int> UnreadMessagesUpdated;
        event Action<Dictionary<string, ChatPresence>> OnlinePresenceUpdated;
        event Action<Dictionary<string, ChatPresence>> UserTypingEvent;

        Dictionary<string, ChatMessage> Messages { get; }
        Dictionary<string, ChatPresence> OnlineUsers { get; }
        Dictionary<string, ChatPresence> TypingUsers { get; }
        bool IsTyping { set; }

        bool IsChatConnected();
        void HandleNoInternetConnection();
        void UpdateChatConnection();
        bool IsLocalUser(string profileId);
        void SendMessage(ChatMessageRequest chatMessageRequest, Action success = null, Action failure = null);
        bool AlreadySentHelp(ChatMessage message);
        bool HasOwnReactionOnMessage(ChatMessage message, string reactionType);
        void SendReaction(ChatMessage message, string reactionUid, Action success = null, Action failure = null);
        void DeleteMessage(ChatMessage message, Action success = null, Action failure = null);
        void SetLocalHelpSent(ChatMessage message);
        void ClaimIap(ChatMessage message, Action success = null, Action failure = null);
        void SetLocalIapClaimed(ChatMessage message);
        bool AlreadyClaimedIap(ChatMessage message);
        void AddLocalMessage(MessageType messageType);
        void UploadGifToChat(string path, Action<ChatMessageAttachementUploaded[]> success, Action failure);
        void UploadFileToChat(string path, Action<ChatMessageAttachementUploaded> success, Action failure);
        void UploadVideoToChat(string path, Action<ChatMessageAttachementUploaded[]> success, Action failure);
        void MarkAllAsRead();
        void FlagMessage(ChatMessage message, Action success = null, Action failure = null);
        string GetLanguage();
    }
}