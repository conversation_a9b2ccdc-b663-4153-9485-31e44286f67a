using System;
using BBB;
using BBB.DI;
using BebopBee;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Hud
{
    public class ProfileTopBarWidget : TopBarWidget
    {
        [SerializeField] private AsyncAvatar _avatarImage;
        [SerializeField] private Button _button;
        [SerializeField] private NotifierWidget _badgesNotifierWidget;

        public event Action OnClicked;

        private IAccountManager _accountManager;
        private GameNotificationManager _notificationManager;

        private void Awake()
        {
            _button.ReplaceOnClick(() => OnClicked.SafeInvoke());
        }

        public override void Init(IContext context)
        {
            base.Init(context);
            _notificationManager = context.Resolve<GameNotificationManager>();
            _accountManager = context.Resolve<IAccountManager>();
            _accountManager.OnAvatarChanged -= SetAvatar;
            _accountManager.OnAvatarChanged += SetAvatar;

            if (_badgesNotifierWidget != null)
            {
                _badgesNotifierWidget.Init(_notificationManager.GetBadgeQuestNotifier());
            }
        }

        public override void Show()
        {
            base.Show();
            SetAvatar();
        }

        private void SetAvatar()
        {
            _avatarImage.ManualStop();
            _avatarImage.Setup(new AvatarInfo(_accountManager.Profile.Avatar, _accountManager.Profile.Country, 
                _accountManager.Profile.AvatarFrame, _accountManager.Profile.BadgeUid));
        }

        public override void ShowBalance()
        {
        }

        public override void HideBalance()
        {
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            if (_accountManager != null)
            {
                _accountManager.OnAvatarChanged -= SetAvatar;
            }
        }
    }
}