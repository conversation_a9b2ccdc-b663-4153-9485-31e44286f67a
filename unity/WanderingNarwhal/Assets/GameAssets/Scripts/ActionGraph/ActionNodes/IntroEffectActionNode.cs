using System;
using System.Collections.Generic;
using System.Threading;
using BBB.Core;
using Bebopbee.Core.UI;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using GameAssets.Scripts.uNode;
using UnityEngine;
using UnityEngine.UI;
using XNode;

namespace BBB.ActionGraph.ActionNodes
{
    [Node.CreateNodeMenu("Actions/Intro Effect")]
    public class IntroEffectActionNode: BaseEffectActionNode
    {
        [Required]
        [Input(backingValue = ShowBackingValue.Never, connectionType = ConnectionType.Override)]
        [SerializeField] private RectMask2D _clipRect;
        [SerializeField] private float _vfxDelay;
        [SerializeField] public bool _showVfx;
        
        private readonly List<ParticleSystem> _activeIntroParticles = new();
        private readonly List<ParticleSystem> _activeFallingStarsParticles = new();
        private Tween _activeTween;

        protected override UniTask ExecuteInternal(ActionGraphContext graphContext, CancellationToken cancellationToken)
        {
            var clipRect = GetInputValue<RectMask2D>(nameof(_clipRect));
            if (!_showVfx || clipRect == null)
                return UniTask.CompletedTask;//for generic multifunctional nodes
            
            try
            {
                if (clipRect.GetComponentInChildren<Touchable>() != null)
                {
                    PlayIntro(clipRect, _vfxDelay, cancellationToken).Forget();
                }
                else
                {
                    PlayFallingStars(clipRect, cancellationToken).Forget();
                }

                ApplyMaterial(clipRect.transform, EpisodeSceneResourceManager.Instance.SceneTaskIntro, graphContext);

                var top = clipRect.rectTransform.sizeDelta.y;
                SetMaskRectTop(clipRect, top);
                clipRect.enabled = true;
                _activeTween = DOTween.To(() => 0f,
                        progress =>
                        {
                            var positionY = (1f - 2 * progress) * clipRect.rectTransform.sizeDelta.y;
                            SetMaskRectTop(clipRect, positionY);
                        },
                        1f, 1)
                    .OnComplete(() =>
                    {
                        RestoreMaterials();
                        DestroyMaterials();
                    });
            }
            finally
            {
                // Cleanup will happen in OnCanceled if canceled
            }

            return UniTask.CompletedTask;
        }

        protected override void OnCanceled()
        {
            if (_activeTween != null && _activeTween.IsActive())
            {
                _activeTween.Kill();
                _activeTween = null;
            }
            
            RestoreMaterials();
            DestroyMaterials();
            
            foreach (var particle in _activeIntroParticles)
            {
                ReturnParticle(particle);
                if (particle != null)
                    EpisodeSceneResourceManager.Instance.ReturnIntroFx(particle);
            }
            _activeIntroParticles.Clear();
            
            foreach (var particle in _activeFallingStarsParticles)
            {
                ReturnParticle(particle);
                if (particle != null)
                    EpisodeSceneResourceManager.Instance.ReturnFallingStarsFx(particle);
            }
            _activeFallingStarsParticles.Clear();
        }

        private async UniTask PlayIntro(RectMask2D clipRect, float vfxDelay, CancellationToken cancellationToken)
        {
            await UniTask.Delay(TimeSpan.FromSeconds(vfxDelay), cancellationToken: cancellationToken);
            PlayNodeBuildVfx(ParticleSystemType.Intro, clipRect, _activeIntroParticles);

            await UniTask.Delay(TimeSpan.FromSeconds(_activeIntroParticles[0].main.duration), cancellationToken: cancellationToken);
            foreach (var activeParticle in _activeIntroParticles)
            {
                ReturnParticle(activeParticle);
                EpisodeSceneResourceManager.Instance.ReturnIntroFx(activeParticle);
            }
            _activeIntroParticles.Clear();
        }
        
        private async UniTask PlayFallingStars(RectMask2D clipRect, CancellationToken cancellationToken)
        {
            PlayNodeBuildVfx(ParticleSystemType.FallingStars, clipRect, _activeFallingStarsParticles);
            foreach (var activeFallingStarsParticle in _activeFallingStarsParticles)
            {
                activeFallingStarsParticle.Play(true);
            }

            await UniTask.Delay(TimeSpan.FromSeconds(_activeFallingStarsParticles[0].main.duration), cancellationToken: cancellationToken);
            foreach (var activeParticle in _activeFallingStarsParticles)
            {
                ReturnParticle(activeParticle);
                EpisodeSceneResourceManager.Instance.ReturnFallingStarsFx(activeParticle);
            }
            _activeFallingStarsParticles.Clear();
        }
        
        private void PlayNodeBuildVfx(ParticleSystemType type, RectMask2D clipRect, List<ParticleSystem> activeParticles)
        {
            var transformParent = clipRect.transform.parent;
            var markers = transformParent.GetComponentsInChildren<IntroVfxMarker>();
            foreach (var marker in markers)
            {
                var introFx = SetupParticleSystem(GetParticleBy(type), marker.RectTransform());
                activeParticles.Add(introFx);
            }

            if (activeParticles.Count == 0)
            {
                var introFx = SetupParticleSystem(GetParticleBy(type), clipRect.RectTransform());
                activeParticles.Add(introFx);
            }
        }

        private ParticleSystem GetParticleBy(ParticleSystemType type)
        {
            switch (type)
            {
                case ParticleSystemType.Intro: return EpisodeSceneResourceManager.Instance.GarbFx_change;
                case ParticleSystemType.FallingStars: return EpisodeSceneResourceManager.Instance.ObjAppear_Fx;
                default:
                    BDebug.LogError(LogCat.Flow, $"Unknown particle system type: {type}");
                    return EpisodeSceneResourceManager.Instance.GarbFx_change;
            }
        }

        private void ReturnParticle(ParticleSystem particleSystem)
        {
            if (particleSystem == null) return;
            particleSystem.Stop(true, ParticleSystemStopBehavior.StopEmittingAndClear);
            particleSystem.gameObject.SetActive(false);
        }

        private ParticleSystem SetupParticleSystem(ParticleSystem particleSystem, RectTransform rect)
        {
            var rectTransform = (RectTransform)particleSystem.transform;
            rectTransform.SetParent(rect, false);
            rectTransform.anchorMin = new Vector2(0, 0);  
            rectTransform.anchorMax = new Vector2(1, 1);
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;
            particleSystem.gameObject.SetActive(true);
            return particleSystem;
        }

        private void SetMaskRectTop(RectMask2D mask, float top)
        {
            var clipRectPadding = mask.padding;
            clipRectPadding.w = top;
            clipRectPadding.y = 0;
            mask.padding = clipRectPadding;
        }

        private enum ParticleSystemType
        {
            Intro,
            FallingStars,
        }
    }
}