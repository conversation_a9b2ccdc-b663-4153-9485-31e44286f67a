using System;
using System.Collections.Generic;
using System.Threading;
using BBB;
using BBB.BrainCloud;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.DI;
using BBB.Generic.Modal;
using BBB.Social;
using BebopBee;
using Cysharp.Threading.Tasks;
using FBConfig;
using GameAssets.Scripts.ChallengeModal;
using PBGame;
using RPC.Teams;
using TeamMemberData = BBB.TeamMemberData;

namespace GameAssets.Scripts.GlobeModal
{
    public class GlobeModalController : BaseModalsController<IGlobeViewPresenter>
    {
        private static readonly List<string> FallbackGroups = new() { "SA", "EU", "AS", "AF" };

        private const int OtherChallengesNumber = 5;
        private const float CandidatesFetchFailDelay = 2f;

        private readonly Dictionary<string, ChallengeLocationConfig> _challengeLocationConfigByCountryCode = new();
        private readonly Dictionary<string, ChallengeLocationConfig> _fallbackLocationConfigByRegion = new();
        private IDictionary<string, ChallengeLocationConfig> _challengeLocationConfigs;

        private readonly List<ChallengeInfo> _challenges = new();
        private readonly List<ChallengeInfo> _otherChallenges = new();
        private readonly HashSet<string> _cachedUidsToSkip = new();

        private IModalsBuilder _modalsBuilder;
        private ChallengeTriviaManager _challengeTriviaManager;
        private SdbManager _sdbManager;
        private BrainCloudManager _brainCloudManager;
        private IAccountManager _accountManager;
        private GenericModalFactory _genericModalFactory;

        private ChallengeModalController _challengeModalController;
        private ChallengeLocationConfig? _fallbackLocation;

        private Action _hideDelegate;

        private CancellationTokenSource _waitCts;
        private bool _failedToLoadCandidates;

        protected override bool UseEasyTouchUICompatibility => false;

        public override void Init(IContext previousContext)
        {
            base.Init(previousContext);

            _challengeLocationConfigs = previousContext.Resolve<IConfig>().Get<ChallengeLocationConfig>();
            _challengeLocationConfigByCountryCode.Clear();
            foreach (var keyValuePair in _challengeLocationConfigs)
            {
                _challengeLocationConfigByCountryCode[keyValuePair.Value.CountryCode] = keyValuePair.Value;

                if (keyValuePair.Value.DefaultInRegion)
                {
                    _fallbackLocationConfigByRegion[keyValuePair.Value.GroupUid] = keyValuePair.Value;
                }

                _fallbackLocation ??= keyValuePair.Value;
            }

            _modalsBuilder = previousContext.Resolve<IModalsBuilder>();
            _brainCloudManager = previousContext.Resolve<BrainCloudManager>();
            _accountManager = previousContext.Resolve<IAccountManager>();

            _challengeTriviaManager = previousContext.Resolve<ChallengeTriviaManager>();
            _sdbManager = previousContext.Resolve<SdbManager>();
            _genericModalFactory = previousContext.Resolve<GenericModalFactory>();
        }

        public void Setup(Action hideDelegate)
        {
            _hideDelegate = hideDelegate;
        }

        protected override void OnShow()
        {
            base.OnShow();
            Subscribe();

            LoadCandidates();
            InitOtherChallenges();
        }

        protected override void OnHide()
        {
            base.OnHide();
            Unsubscribe();
        }

        private void Subscribe()
        {
            Unsubscribe();
            View.SendChallengeButton += SendChallengeButtonHandler;
            View.OtherPlayersButton += OtherPlayersButtonHandler;
            View.OnCancelButton += OnCancelSendingHandler;
        }

        private void Unsubscribe()
        {
            View.SendChallengeButton -= SendChallengeButtonHandler;
            View.OtherPlayersButton -= OtherPlayersButtonHandler;
            View.OnCancelButton -= OnCancelSendingHandler;
        }

        private void ResetWaiting()
        {
            if (_waitCts == null) return;
            
            if (!_waitCts.IsCancellationRequested)
            {
                _waitCts.Cancel();
            }
            _waitCts = null;
        }

        private void InitOtherChallenges()
        {
            _cachedUidsToSkip.Clear();
            _otherChallenges.Clear();

            // excluding own uids
            _cachedUidsToSkip.Add(_accountManager.Profile.Uid);
            _cachedUidsToSkip.Add(_brainCloudManager.ProfileId);

            var alreadyPlayingOpponentIds = _challengeTriviaManager.GetAlreadyPlayingOpponentIds();
            var favorites = _challengeTriviaManager.FavoritesEntities;
            var remainingPlayers = OtherChallengesNumber;

            if (favorites != null)
            {
                foreach (var favorite in favorites.Values)
                {
                    if (remainingPlayers == 0)
                        break;

                    var profileData = favorite.Data?.ToObject<BCPlayerProfileData>();
                    var playerId = profileData?.UserId;
                    if (playerId.IsNullOrEmpty() || _cachedUidsToSkip.Contains(playerId))
                        continue;

                    var alreadyPlaying = alreadyPlayingOpponentIds.Contains(playerId);
                    var limitReached = _challengeTriviaManager.CheckPlayerChallengesLimitReached(playerId);
                    var challengeInfo = CreateChallengeInfo(profileData, true, alreadyPlaying, limitReached);
                    _otherChallenges.Add(challengeInfo);
                    _cachedUidsToSkip.Add(playerId);

                    remainingPlayers--;
                }
            }

            var teamMembers = new HashSet<string>();
            if (_accountManager.IsInTeam && !_accountManager.Profile.CurrentTeam.Members.IsNullOrEmpty())
            {
                foreach (var teamMemberData in _accountManager.Profile.CurrentTeam.Members)
                {
                    var playerId = teamMemberData?.Uid;
                    if (playerId.IsNullOrEmpty()) continue;

                    teamMembers.Add(playerId);
                }
            }


            var activeSuggestions = _challengeTriviaManager.ActiveSuggestions;
            foreach (var activeSuggestion in activeSuggestions.Values)
            {
                if (remainingPlayers == 0)
                    break;

                var friendData = activeSuggestion.FriendData;
                var playerId = friendData.UserId;
                if (playerId.IsNullOrEmpty() || _cachedUidsToSkip.Contains(playerId)) continue;

                var isTeamMember = teamMembers.Contains(playerId);
                var alreadyPlaying = alreadyPlayingOpponentIds.Contains(playerId);
                var limitReached = _challengeTriviaManager.CheckPlayerChallengesLimitReached(playerId);
                var challengeInfo = CreateChallengeInfo(friendData, isTeamMember, alreadyPlaying, limitReached);
                _otherChallenges.Add(challengeInfo);
                _cachedUidsToSkip.Add(playerId);

                remainingPlayers--;
            }

            if (_accountManager.IsInTeam && !_accountManager.Profile.CurrentTeam.Members.IsNullOrEmpty())
            {
                var membersCount = _accountManager.Profile.CurrentTeam.Members.Count;
                for (var i = 0; i < membersCount; i++)
                {
                    if (remainingPlayers == 0)
                        break;

                    var teamMemberData = _accountManager.Profile.CurrentTeam.Members[i];
                    var playerId = teamMemberData?.Uid;
                    if (playerId.IsNullOrEmpty() || _cachedUidsToSkip.Contains(playerId))
                        continue;

                    var alreadyPlaying = alreadyPlayingOpponentIds.Contains(playerId);
                    var limitReached = _challengeTriviaManager.CheckPlayerChallengesLimitReached(playerId);
                    var challengeInfo = CreateChallengeInfo(teamMemberData, alreadyPlaying, limitReached);
                    _otherChallenges.Add(challengeInfo);
                    _cachedUidsToSkip.Add(playerId);

                    remainingPlayers--;
                }
            }

            var bots = _challengeTriviaManager.BotEntities;
            foreach (var profileData in bots)
            {
                if (remainingPlayers == 0)
                    break;

                var playerId = profileData?.UserId;
                // if for some reason bot was in favorite, we need to avoid re-adding
                if (playerId.IsNullOrEmpty() || _cachedUidsToSkip.Contains(playerId))
                    continue;

                var alreadyPlaying = alreadyPlayingOpponentIds.Contains(playerId);
                var challengeInfo = CreateChallengeInfo(profileData, false, alreadyPlaying);
                _otherChallenges.Add(challengeInfo);
                _cachedUidsToSkip.Add(playerId);

                remainingPlayers--;
            }
        }

        private ChallengeInfo CreateChallengeInfo(BCPlayerProfileData profileData, bool favorite = false, bool alreadyPlaying = false, bool limitReached = false) =>
            new(profileData, GetLocationConfigByCountry(profileData.SummaryFriendData.Country), _challengeTriviaManager.GetChallengesPlayed(profileData.UserId), favorite, alreadyPlaying,
                limitReached);

        private ChallengeInfo CreateChallengeInfo(TeamMemberData teamMemberData, bool alreadyPlaying, bool limitReached = false) =>
            new(teamMemberData, GetLocationConfigByCountry(teamMemberData.Country), _challengeTriviaManager.GetChallengesPlayed(teamMemberData.Uid), false, alreadyPlaying, limitReached);

        private ChallengeInfo CreateChallengeInfo(PBFriendData friendData, bool teamMember, bool alreadyPlaying, bool limitReached = false) =>
            new(friendData, GetLocationConfigByCountry(friendData.Country), _challengeTriviaManager.GetChallengesPlayed(friendData.UserId), teamMember, false, alreadyPlaying, limitReached);

        private ChallengeLocationConfig GetLocationConfigByCountry(string country)
        {
            if (_challengeLocationConfigByCountryCode.TryGetValue(country, out var locationConfig))
                return locationConfig;

            var fallbackRegion = FallbackGroups.GetRandomItem();
            if (_fallbackLocationConfigByRegion.TryGetValue(fallbackRegion, out locationConfig))
                return locationConfig;

            BDebug.LogError(LogCat.Globe, $"Couldn't find location config with groupUid {fallbackRegion}");
            locationConfig = _fallbackLocation!.Value;

            return locationConfig;
        }

        private void LoadCandidates()
        {
            ResetWaiting();
            _failedToLoadCandidates = false;
            if (_challengeTriviaManager.GetLoadedCandidatesOrLoad(CandidatesLoadedHandler, CandidatesFailedToLoadHandler)) return;

            View.ShowLoadingScreen(true);
            WaitForDataFetch().Forget();
        }
        
        private async UniTaskVoid WaitForDataFetch()
        {
            ResetWaiting();
            var waitCts = new CancellationTokenSource();
            _waitCts = waitCts;

            try
            {
                await UniTask.WaitForSeconds(CandidatesFetchFailDelay, cancellationToken: waitCts.Token);

                DisposeCts();
                CandidatesFailedToLoadHandler();
            }
            catch (Exception ex)
            {
                DisposeCts();
                if (ex is not OperationCanceledException)
                {
                    BDebug.LogError(LogCat.General, $"Globe modal wait failed: {ex}");
                }
            }
            return;

            void DisposeCts()
            {
                waitCts.Dispose();
                if (_waitCts == waitCts)
                {
                    _waitCts = null;
                }
            }
        }

        private void CandidatesLoadedHandler(BCListCandidatesResponse response)
        {
            if (_failedToLoadCandidates)
                return;

            ResetWaiting();
            var candidates = response.Response.Data.Candidates;
            if (candidates.IsNullOrEmpty())
            {
                CandidatesFailedToLoadHandler();
                return;
            }

            _challenges.Clear();

            _cachedUidsToSkip.Clear();
            // excluding uids of already challenged opponents from candidates
            var activeChallenges = _challengeTriviaManager.ChallengeEntities;
            foreach (var activeChallenge in activeChallenges)
            {
                _cachedUidsToSkip.Add(activeChallenge.Value.Opponent.UserId);
            }

            foreach (var profileData in candidates)
            {
                var playerId = profileData?.UserId;
                if (playerId.IsNullOrEmpty() || _cachedUidsToSkip.Contains(playerId))
                    continue;

                var challengeInfo = CreateChallengeInfo(profileData);
                _challenges.Add(challengeInfo);
                _cachedUidsToSkip.Add(playerId);
            }

            if (_challenges.IsNullOrEmpty())
            {
                CandidatesFailedToLoadHandler();
                return;
            }

            View.Setup(_challenges);
            View.ShowLoadingScreen(false);
        }

        private void CandidatesFailedToLoadHandler()
        {
            if (_failedToLoadCandidates)
                return;

            _failedToLoadCandidates = true;
            ResetWaiting();
            View.ShowLoadingScreen(false);
            _genericModalFactory.ShowNoConnectionModal(_ => TryShowSdbRewardModal());
        }

        private void SendChallengeButtonHandler(ChallengeInfo challengeInfo)
        {
            View.ShowLoadingScreen(true);
            _challengeTriviaManager.StartChallenge(challengeInfo.PlayerUid, StartChallengeSuccessHandler, StartChallengeFailureHandler);
        }

        private void StartChallengeSuccessHandler()
        {
            View.SetChallengeSentState(TryShowSdbRewardModal);
            View.ShowLoadingScreen(false);
        }

        private void StartChallengeFailureHandler()
        {
            View.ShowLoadingScreen(false);
            _genericModalFactory.ShowNoConnectionModal(_ => TryShowSdbRewardModal());
        }

        private void OnCancelSendingHandler()
        {
            TryShowSdbRewardModal();
        }

        private void TryShowSdbRewardModal()
        {
            if (!_sdbManager.SdbUnlockedAfterLevel)
            {
                InvokeHideDelegate();
                return;
            }

            _sdbManager.ShowSdbRewardModal(InvokeHideDelegate);
        }

        private void OtherPlayersButtonHandler()
        {
            _challengeModalController = _modalsBuilder.CreateModalView<ChallengeModalController>(ModalsType.ChallengeModal);
            _challengeModalController.SetupNewChallenges(_otherChallenges, ChallengeSelectedHandler);
            _challengeModalController.ShowModal();
        }

        private void ChallengeSelectedHandler(ChallengeInfo challengeInfo)
        {
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.Didi.Name, DauInteractions.Didi.ChallengeScreen,
                DauInteractions.Didi.Challenge));
            View.SelectChallenge(challengeInfo);
            _challengeModalController.Hide();
        }

        protected override void OnPostHide()
        {
            base.OnPostHide();
            ResetWaiting();
        }

        private void InvokeHideDelegate()
        {
            if (_hideDelegate != null)
            {
                _hideDelegate.Invoke();
                _hideDelegate = null;
            }
            else
            {
                HideModal();
            }
        }
    }
}