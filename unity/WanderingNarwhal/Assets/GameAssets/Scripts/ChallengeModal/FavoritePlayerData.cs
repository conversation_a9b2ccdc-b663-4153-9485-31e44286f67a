using BBB.BrainCloud;
using PBGame;
using BBB;
using BebopBee.Social;

namespace GameAssets.Scripts.ChallengeModal
{
    public class FavoritePlayerData
    {
        public readonly string PlayerUid;
        public readonly string PlayerName;
        public readonly string AvatarUrl;
        public readonly string CountryCode;
        public readonly int PlayerRank;
        public readonly int ChallengesPlayed;
        public readonly string AvatarFrame;
        public bool Favorite { get; set; }

        private readonly BCPlayerProfileData _profileData;

        public FavoritePlayerData(TeamMemberData teamMemberData, int challengesPlayed, bool favorite)
        {
            _profileData = null;

            PlayerUid = teamMemberData.Uid;
            PlayerName = teamMemberData.Name;
            AvatarUrl = teamMemberData.Avatar;
            CountryCode = teamMemberData.Country;
            PlayerRank = teamMemberData.Trophies;
            ChallengesPlayed = challengesPlayed;
            Favorite = favorite;
            AvatarFrame = teamMemberData.AvatarFrame;
        }

        public FavoritePlayerData(BCPlayerProfileData profileData, int challengesPlayed, bool favorite)
        {
            _profileData = profileData;

            PlayerUid = profileData.UserId;
            PlayerName = profileData.UserName;
            AvatarUrl = profileData.AvatarUrl;
            CountryCode = profileData.SummaryFriendData.Country;
            PlayerRank = profileData.SummaryFriendData.Trophies;
            ChallengesPlayed = challengesPlayed;
            Favorite = favorite;
            AvatarFrame = profileData.SummaryFriendData.AvatarFrame;
        }
        
        public FavoritePlayerData(PBFriendData friendData, int challengesPlayed, bool favorite)
        {
            _profileData = null;

            PlayerUid = friendData.UserId;
            PlayerName = friendData.UserName;
            AvatarUrl = friendData.AvatarUrl;
            CountryCode = friendData.Country;
            PlayerRank = friendData.Trophies;
            ChallengesPlayed = challengesPlayed;
            Favorite = favorite;
            AvatarFrame = ProfileUtils.DefaultFrameUid;
        }

        public BCPlayerProfileData GetBCPlayerProfileData()
        {
            if (_profileData != null)
                return _profileData;

            // handling team members only if needed, as it is not a struct anymore
            return new BCPlayerProfileData
            {
                UserId = PlayerUid,
                UserName = PlayerName,
                AvatarUrl = AvatarUrl,
                SummaryFriendData = new BCSummaryFriendData()
                {
                    Uid = PlayerUid,
                    Trophies = PlayerRank,
                    Country = CountryCode,
                    AvatarFrame = AvatarFrame
                }
            };
        }
    }
}