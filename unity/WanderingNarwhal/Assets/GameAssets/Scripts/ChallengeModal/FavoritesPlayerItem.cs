using System;
using BBB;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.ChallengeModal
{
    public class FavoritesPlayerItem : BbbMonoBehaviour
    {
        [SerializeField] private AsyncAvatar _asyncAvatar;
        [SerializeField] private GameObject _favoriteHolder;

        [SerializeField] private TextMeshProUG<PERSON> _playerName;
        [SerializeField] private TextMeshProUGUI _playerRank;
        [SerializeField] private LocalizedTextPro _challengesPlayed;

        [SerializeField] private Button _removeButton;
        [SerializeField] private Button _addButton;
        [SerializeField] private Button _discardButton;

        private FavoritePlayerData _favoritePlayerData;

        private Action<FavoritePlayerData> _removeButtonCallback;
        private Action<FavoritePlayerData> _addButtonCallback;
        private Action<FavoritePlayerData> _discardButtonCallback;

        public void Init(Action<FavoritePlayerData> removeButtonCallback, Action<FavoritePlayerData> addButtonCallback, Action<FavoritePlayerData> discardButtonCallback)
        {
            _removeButtonCallback = removeButtonCallback;
            _addButtonCallback = addButtonCallback;
            _discardButtonCallback = discardButtonCallback;

            _removeButton.ReplaceOnClick(RemoveButtonHandler);
            _addButton.ReplaceOnClick(AddButtonHandler);
            _discardButton.ReplaceOnClick(DiscardButtonHandler);
        }

        public void Setup(FavoritePlayerData favoritePlayerData)
        {
            var favorite = favoritePlayerData.Favorite;
            _removeButton.gameObject.SetActive(favorite);
            _addButton.gameObject.SetActive(!favorite);
            _discardButton.gameObject.SetActive(!favorite);

            _favoriteHolder.SetActive(favorite);

            _favoritePlayerData = favoritePlayerData;

            _asyncAvatar.Setup(new AvatarInfo(favoritePlayerData.AvatarUrl, favoritePlayerData.CountryCode, favoritePlayerData.AvatarFrame));
            _playerName.text = favoritePlayerData.PlayerName;
            _playerRank.text = favoritePlayerData.PlayerRank.ToString();

            if (favoritePlayerData.ChallengesPlayed > 0)
            {
                _challengesPlayed.FormatSetArgs(favoritePlayerData.ChallengesPlayed);
                _challengesPlayed.gameObject.SetActive(true);
            }
            else
            {
                _challengesPlayed.gameObject.SetActive(false);
            }
        }

        private void RemoveButtonHandler()
        {
            _removeButtonCallback?.Invoke(_favoritePlayerData);
        }

        private void AddButtonHandler()
        {
            _addButtonCallback?.Invoke(_favoritePlayerData);
        }

        private void DiscardButtonHandler()
        {
            _discardButtonCallback?.Invoke(_favoritePlayerData);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _removeButtonCallback = null;
            _addButtonCallback = null;
            _discardButtonCallback = null;
        }
    }
}