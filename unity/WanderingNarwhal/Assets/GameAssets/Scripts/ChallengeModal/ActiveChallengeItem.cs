using System;
using BBB;
using Bebopbee.Core.Systems.RpcCommandManager;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.ChallengeModal
{
    public class ActiveChallengeItem : BbbMonoBehaviour
    {
        [SerializeField] private AsyncAvatar _asyncAvatar;
        [SerializeField] private GameObject _favoriteHolder;
        [SerializeField] private TextMeshProUGUI _playerName;
        [SerializeField] private TextMeshProUGUI _lastUpdatedTime;

        [SerializeField] private GameObject _challengeNewText;
        [SerializeField] private GameObject _waitingForYourTurnText;
        [SerializeField] private GameObject _waitingForTheirTurnText;
        [SerializeField] private GameObject _challengeFailedText;
        [SerializeField] private GameObject _challengeCompletedText;
        [SerializeField] private GameObject _challengeExpiredText;
        [SerializeField] private GameObject _challengeDeclinedText;
        
        [SerializeField] private GameObject _buttonNotificationRoot;
        [SerializeField] private GameObject _itemNotificationRoot;

        [SerializeField] private GameObject _progressBarHolder;
        [SerializeField] private Image _progressBarFillerImage;
        [SerializeField] private LocalizedTextPro _progressBarText;

        [SerializeField] private Button _playButton;
        [SerializeField] private Button _claimButton;
        [SerializeField] private Button _nudgeActiveButton;
        [SerializeField] private Button _nudgeInactiveButton;
        [SerializeField] private Button _expireButton;
        [SerializeField] private Button _discardButton;
        
        [SerializeField] private Transform _floatingTextAnchor;

        private Action<ActiveChallengeData> _playButtonCallback;
        private Action<ActiveChallengeData> _claimButtonCallback;
        private Action<ActiveChallengeData> _nudgeButtonCallback;
        private Action<ActiveChallengeData, Transform> _alreadyNudgedButtonCallback;
        private Action<ActiveChallengeData> _expireButtonCallback;
        private Action<ActiveChallengeData> _discardButtonCallback;
        private ActiveChallengeData _activeChallengeData;
        private ITimeController _timeController;

        public void Init(
            ITimeController timeController,
            Action<ActiveChallengeData> playButtonCallback,
            Action<ActiveChallengeData> claimButtonCallback,
            Action<ActiveChallengeData> nudgeButtonCallback,
            Action<ActiveChallengeData, Transform> alreadyNudgedButtonCallback,
            Action<ActiveChallengeData> expireButtonCallback,
            Action<ActiveChallengeData> discardButtonCallback)
        {
            _timeController = timeController;

            _playButtonCallback = playButtonCallback;
            _claimButtonCallback = claimButtonCallback;
            _nudgeButtonCallback = nudgeButtonCallback;
            _alreadyNudgedButtonCallback = alreadyNudgedButtonCallback;
            _expireButtonCallback = expireButtonCallback;
            _discardButtonCallback = discardButtonCallback;

            _playButton.ReplaceOnClick(PlayButtonHandler);
            _claimButton.ReplaceOnClick(ClaimButtonHandler);
            _nudgeActiveButton.ReplaceOnClick(NudgeButtonHandler);
            _nudgeInactiveButton.ReplaceOnClick(AlreadyNudgedButtonHandler);
            _expireButton.ReplaceOnClick(ExpireButtonHandler);
            _discardButton.ReplaceOnClick(DiscardButtonHandler);
        }

        public void Setup(ActiveChallengeData activeChallengeData, int maxProgress)
        {
            _activeChallengeData = activeChallengeData;

            _asyncAvatar.Setup(new AvatarInfo(activeChallengeData.AvatarUrl, activeChallengeData.CountryCode, activeChallengeData.AvatarFrameUid));
            _favoriteHolder.SetActive(activeChallengeData.Favorite);
            _playerName.text = activeChallengeData.PlayerName;

            _challengeNewText.SetActive(false);
            _waitingForYourTurnText.SetActive(false);
            _waitingForTheirTurnText.SetActive(false);
            _challengeFailedText.SetActive(false);
            _challengeCompletedText.SetActive(false);
            _challengeExpiredText.SetActive(false);
            _challengeDeclinedText.SetActive(false);
            _progressBarHolder.SetActive(false);

            _playButton.gameObject.SetActive(false);
            _claimButton.gameObject.SetActive(false);
            _nudgeActiveButton.gameObject.SetActive(false);
            _nudgeInactiveButton.gameObject.SetActive(false);
            _expireButton.gameObject.SetActive(false);
            _discardButton.gameObject.SetActive(false);
            
            _buttonNotificationRoot.SetActive(false);
            _itemNotificationRoot.SetActive(false);

            var secondsSinceLastActivity = _timeController.CurrentTimeStamp() - activeChallengeData.LastUpdatedTimestamp;
            _lastUpdatedTime.text = secondsSinceLastActivity.SecondsToStringFormat(TimeStringFormatType.All, 1,false);

            var progress = (float)activeChallengeData.RoundNumber / maxProgress;
            _progressBarFillerImage.fillAmount = progress;
            var roundNumber = activeChallengeData.RoundNumber / 2;
            var maxRoundNumber = maxProgress / 2;
            _progressBarText.FormatSetArgs(roundNumber, maxRoundNumber);

            switch (activeChallengeData.State)
            {
                case ActiveChallengeState.NewChallenge:
                    _challengeNewText.SetActive(true);

                    _playButton.gameObject.SetActive(true);
                    _discardButton.gameObject.SetActive(true);
                    _buttonNotificationRoot.SetActive(!activeChallengeData.Seen);
                    break;
                case ActiveChallengeState.WaitingForYourTurn:
                    _waitingForYourTurnText.SetActive(true);
                    _progressBarHolder.SetActive(true);

                    _playButton.gameObject.SetActive(true);
                    _buttonNotificationRoot.SetActive(!activeChallengeData.Seen);
                    break;
                case ActiveChallengeState.WaitingForTheirTurn:
                    _waitingForTheirTurnText.SetActive(true);
                    _progressBarHolder.SetActive(true);

                    _nudgeActiveButton.gameObject.SetActive(activeChallengeData.CanNudge);
                    _nudgeInactiveButton.gameObject.SetActive(activeChallengeData.AlreadyNudged);
                    break;
                case ActiveChallengeState.FailedByThem:
                    _challengeFailedText.SetActive(true);
                    _progressBarHolder.SetActive(true);

                    var isClaimable = activeChallengeData.RoundNumber > 0;
                    _claimButton.gameObject.SetActive(isClaimable);
                    if (isClaimable)
                    {
                        _buttonNotificationRoot.SetActive(!activeChallengeData.Seen);
                    }
                    else
                    {
                        _itemNotificationRoot.SetActive(!activeChallengeData.Seen);
                    }
                    break;
                case ActiveChallengeState.ChallengeCompleted:
                    _challengeCompletedText.SetActive(true);
                    _progressBarHolder.SetActive(true);

                    _claimButton.gameObject.SetActive(true);
                    _buttonNotificationRoot.SetActive(!activeChallengeData.Seen);
                    break;
                case ActiveChallengeState.Expired:
                    _challengeExpiredText.SetActive(true);

                    if (activeChallengeData.RoundNumber > 0)
                    {
                        _claimButton.gameObject.SetActive(true);
                        break;
                    }
                    _expireButton.gameObject.SetActive(true);
                    _buttonNotificationRoot.SetActive(!activeChallengeData.Seen);
                    break;
                case ActiveChallengeState.DeclinedByThem:
                    _challengeDeclinedText.SetActive(true);

                    _expireButton.gameObject.SetActive(true);
                    _buttonNotificationRoot.SetActive(!activeChallengeData.Seen);
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

        private void PlayButtonHandler()
        {
            MarkSeen();
            _playButtonCallback.Invoke(_activeChallengeData);
        }

        private void ClaimButtonHandler()
        {
            MarkSeen();
            _claimButtonCallback.Invoke(_activeChallengeData);
        }

        private void NudgeButtonHandler()
        {
            _nudgeButtonCallback.Invoke(_activeChallengeData);
        }
        
        private void AlreadyNudgedButtonHandler()
        {
            _alreadyNudgedButtonCallback.Invoke(_activeChallengeData, _floatingTextAnchor);
        }

        private void ExpireButtonHandler()
        {
            MarkSeen();
            _expireButtonCallback.Invoke(_activeChallengeData);
        }

        private void DiscardButtonHandler()
        {
            MarkSeen();
            _discardButtonCallback.Invoke(_activeChallengeData);
        }

        private void MarkSeen()
        {
            _buttonNotificationRoot.SetActive(false);
            _itemNotificationRoot.SetActive(false);
            _activeChallengeData.MarkSeen();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _playButtonCallback = null;
            _claimButtonCallback = null;
            _nudgeButtonCallback = null;
            _alreadyNudgedButtonCallback = null;
            _expireButtonCallback = null; 
            _discardButtonCallback = null;
        }
    }
}