using BBB.BrainCloud;
using BebopBee.Social;

namespace GameAssets.Scripts.ChallengeModal
{
    public class ActiveChallengeData
    {
        public readonly string ChallengeUid;
        public readonly string OwnerUid;
        public readonly long LastUpdatedTimestamp;

        public readonly string PlayerName;
        public readonly string AvatarUrl;
        public readonly string AvatarFrameUid;
        public readonly string CountryCode;
        public readonly int RoundNumber;
        public readonly bool Favorite;

        public readonly ActiveChallengeState State;

        public bool CanNudge { get; private set; }
        public bool AlreadyNudged { get; private set; }
        public bool Seen { get; private set; }

        public ActiveChallengeData(BCChallengeData bcChallengeData, ActiveChallengeState state, bool canNudge, bool alreadyNudged, bool seen, bool favorite)
        {
            ChallengeUid = bcChallengeData.ChallengeId;
            OwnerUid = bcChallengeData.OwnerId;
            LastUpdatedTimestamp = bcChallengeData.LastUpdatedTimestamp;

            PlayerName = bcChallengeData.Opponent.UserName;
            AvatarUrl = bcChallengeData.Opponent.AvatarUrl;
            CountryCode = bcChallengeData.Opponent.SummaryFriendData?.Country ?? ProfileUtils.DefaultCountry;
            RoundNumber = bcChallengeData.RoundNumber;

            State = state;
            CanNudge = canNudge;
            AlreadyNudged = alreadyNudged;
            Seen = seen;
            Favorite = favorite;
            //Avatar Decorations
            AvatarFrameUid = bcChallengeData.Opponent.SummaryFriendData?.AvatarFrame ?? ProfileUtils.DefaultFrameUid;
        }

        public void MarkNudged()
        {
            AlreadyNudged = true;
            CanNudge = false;
        }

        public void MarkSeen()
        {
            Seen = true;
        }
    }
}