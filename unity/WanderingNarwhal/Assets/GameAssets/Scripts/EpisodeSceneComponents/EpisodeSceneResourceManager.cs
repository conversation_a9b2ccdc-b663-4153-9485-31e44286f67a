using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.Core.ResourcesManager;
using BBB.DI;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.ResourceManagement;
using UnityEngine;
using Object = UnityEngine.Object;

namespace GameAssets.Scripts.uNode
{
    public class EpisodeSceneResourceManager : IEpisodeSceneResourceManager, ISpecializedResourceManager, IContextInitializable, IContextReleasable
    {
        private const ScreenType ScreenToLoadResources = ScreenType.EpisodeScreen;
        
        private IAssetsManager _assetsManager;
        
        private bool _isLoading;
        private bool _isFailed;
        private float _progress;

        private ParticleSystem _introFxPrefab;
        private Pool<ParticleSystem> _introFxPool;
        private ParticleSystem _fallingStarsFxPrefab;
        private Pool<ParticleSystem> _fallingStarsFxPool;

        /// <summary>
        /// A small hack to access scene resources from custom node classes
        /// <remarks>
        /// <b>Note:</b> Do NOT cache it anywhere, to avoid potential memory leaks
        /// </remarks>
        /// </summary>
        internal static EpisodeSceneResourceManager Instance { get; private set; }
        
        public bool IsRequired => true;
        public void Reset()
        {
            
        }

        public ParticleSystem GarbFx_change => _introFxPool.Obtain();
        public ParticleSystem ObjAppear_Fx => _fallingStarsFxPool.Obtain();
        public Material SceneTaskIntro { get; private set; } 
        public Material SceneTaskOutro { get; private set; }

        private Transform _freeParticlesHolder;
        
        public void InitializeByContext(IContext context)
        {
            Instance = this;
            if (_freeParticlesHolder == null)
            {
                _freeParticlesHolder = new GameObject(nameof(_freeParticlesHolder).ConvertToGameObjectName()).transform;
            }

            _introFxPool = new Pool<ParticleSystem>(1, () => Object.Instantiate(_introFxPrefab));
            _fallingStarsFxPool = new Pool<ParticleSystem>(1, () => Object.Instantiate(_fallingStarsFxPrefab));
            _assetsManager = context.Resolve<IAssetsManager>();
        }

        public void ReturnIntroFx(ParticleSystem fx)
        {
            fx.transform.SetParent(_freeParticlesHolder);
            _introFxPool.Free(fx);
        }

        public void ReturnFallingStarsFx(ParticleSystem fx)
        {
            fx.transform.SetParent(_freeParticlesHolder);
            _fallingStarsFxPool.Free(fx);
        }
        
        public async UniTask TryReloadAsync(string screenBeingLoaded, ScreenType screenType)
        {
            if ((ScreenToLoadResources & screenType) != screenType)
                return;

            _isFailed = false;
            _progress = 0f;
            _isLoading = true;

            var loads = new List<UniTask>
            {
                LoadAndAssign<GameObject>(nameof(GarbFx_change), go => _introFxPrefab = go.GetComponent<ParticleSystem>()),
                LoadAndAssign<GameObject>(nameof(ObjAppear_Fx),  go => _fallingStarsFxPrefab = go.GetComponent<ParticleSystem>()),
                LoadAndAssign<Material>(nameof(SceneTaskIntro), mat => SceneTaskIntro = mat),
                LoadAndAssign<Material>(nameof(SceneTaskOutro), mat => SceneTaskOutro = mat),
            };

            var total = loads.Count;
            if (total == 0)
            {
                _isLoading = false;
                _progress = 1f;
                return;
            }

            var done = 0;
            try
            {
                foreach (var load in loads)
                {
                    await load;
                    done++;
                    _progress = (float)done / total;
                }
                _isLoading = false;
                _progress = 1f;
            }
            catch (Exception ex)
            {
                _isLoading = false;
                _isFailed = true;
                ProcessRejectionException(ex);
            }
        }
        
        private async UniTask LoadAndAssign<TAsset>(string assetName, Action<TAsset> assignCallback) where TAsset : Object
        {
            var wrapper = await _assetsManager.LoadAsync<TAsset>(assetName);
            var asset = wrapper?.Get();
            if (asset != null)
            {
                assignCallback(asset);
            }
        }

        private static void ProcessRejectionException(Exception ex)
        {
            var message = $"Exception in EpisodeSceneResourceManager: {ex.Message} Stack Trace: {ex.StackTrace}";
            if (ex is AssetsManager.RejectAssetByRestartException)
            {
                BDebug.LogWarning(LogCat.Resources, message);
            }
            else
            {
                BDebug.LogError(LogCat.Resources, message);
            }
        }

        public bool IsLoading(string screenName, string prevScreenName)
        {
            return _isLoading;
        }

        public bool HasFailed()
        {
            return _isFailed;
        }

        public void ResetFailure()
        {
            _isFailed = false;
        }

        public float Progress()
        {
            return _progress;
        }

        public void DisposeForScreen(ScreenType screenType, ScreenType currentScreenType)
        {
            if ((ScreenToLoadResources & currentScreenType) == currentScreenType)
                return;
            
            Clear();
        }

        public void ReleaseByContext(IContext context)
        {
            Instance = null;
            Clear();
        }

        private void Clear()
        {
            if (_freeParticlesHolder != null)
            {
                Object.Destroy(_freeParticlesHolder.gameObject);
            }

            _introFxPool?.Clear();
            _introFxPrefab = null;
            _fallingStarsFxPool?.Clear();
            _fallingStarsFxPrefab = null;
            _assetsManager?.UnloadAsset(SceneTaskIntro);
            SceneTaskIntro = null;
            _assetsManager?.UnloadAsset(SceneTaskOutro);
            SceneTaskOutro = null;
        }
    }
}
