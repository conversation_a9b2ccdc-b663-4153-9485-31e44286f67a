using BBB.BrainCloud;
using BBB.DI;
using BBB.Quests;
using BBB.RaceEvents;
using BBB.UI.Core;
using BebopBee;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.GameEvents.RoyaleEvents.UI;
using TMPro;
using UnityEngine;

namespace GameAssets.Scripts.GenericModals.Tutorial
{
    public class RoyaleEventWinContent : ContextedUiBehaviour
    {
        private const int MaxVisualWinnersCount = 12;
        private const string RewardSharingLocId = "GameEvent_royal_win_modal_reward_sharing_text";

        [SerializeField] private TextMeshProUGUI _prizePoolSize;
        [SerializeField] private TextMeshProUGUI _winSize;
        [SerializeField] private GameObject _otherPlayersSection;
        [SerializeField] private TextMeshProUGUI _rewardSharingText;
        [SerializeField] private AvatarItem _playerAvatar;
        [SerializeField] private Transform _otherPlayersAvatarsRoot;
        [SerializeField] private AvatarItem _avatarItemPrefab;

        private IAccountManager _accountManager;
        private BrainCloudAvatarsManager _brainCloudAvatarsManager;
        private ILocalizationManager _localizationManager;

        protected override void InitWithContextInternal(IContext context)
        {
            _accountManager = context.Resolve<IAccountManager>();
            _brainCloudAvatarsManager = context.Resolve<BrainCloudAvatarsManager>();
            _localizationManager = context.Resolve<ILocalizationManager>();
        }

        public void Setup(RoyaleEvent royaleEvent)
        {
            LazyInit();

            var prizePoolSize = royaleEvent.PrizePoolSize;
            var lastRoundWinnersCount = Mathf.Max(royaleEvent.LastRoundWinnersCount, 1);

            _prizePoolSize.text = prizePoolSize.ToCurrency();

            var playerPrize = prizePoolSize / lastRoundWinnersCount;
            const string regularCoinSuffix = " <sprite name=regular>";
            _winSize.text = playerPrize.ToCurrency() + regularCoinSuffix;

            var otherWinnersCount = lastRoundWinnersCount - 1;
            _otherPlayersSection.SetActive(otherWinnersCount > 0);

            var rewardSharingDescription = _localizationManager.getLocalizedText(RewardSharingLocId);
            _rewardSharingText.text = string.Format(rewardSharingDescription, otherWinnersCount);

            _playerAvatar.Setup(_accountManager.Profile.Avatar, _ => { });
            AddOtherPlayersAvatars(lastRoundWinnersCount).Forget();
        }

        private async UniTask AddOtherPlayersAvatars(int lastRoundWinnersCount)
        {
            _otherPlayersAvatarsRoot.RemoveAllActiveChilden();
            var otherWinnersCount = lastRoundWinnersCount - 1;
            var randomAvatars = await _brainCloudAvatarsManager.GetNumberOfAvatars(otherWinnersCount);
            var visualWinnersCount = Mathf.Min(MaxVisualWinnersCount, otherWinnersCount);
            for (var i = 0; i < visualWinnersCount; i++)
            {
                var otherPlayerAvatar = Instantiate(_avatarItemPrefab, _otherPlayersAvatarsRoot);
                var avatarUrl = randomAvatars[i];
                otherPlayerAvatar.Setup(avatarUrl, _ => { });
            }
        }
    }
}