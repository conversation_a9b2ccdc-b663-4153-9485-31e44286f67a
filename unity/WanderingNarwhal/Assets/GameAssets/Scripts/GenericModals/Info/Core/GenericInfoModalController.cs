using BBB.DI;
using BebopBee.Core.UI;
using GameAssets.Scripts.UI.OverlayDialog;

namespace GameAssets.Scripts.GenericModals.Info.Core
{
    public class GenericInfoModalController : BaseGenericModalController<IGenericInfoViewPresenter, GenericInfoModel, GenericInfoModalConfig>, IModalControllerFacade
    {
        private IOverlayDialogManager _overlayDialogManager;
        private readonly OverlayDialogConfig _overlayDialogConfig = new();

        protected override void OnInitializeByContext(IContext context)
        {
            base.OnInitializeByContext(context);
            _overlayDialogManager = context.Resolve<IOverlayDialogManager>();
        }

        public new GenericInfoModalController SetupAndShow(GenericInfoModel model, ShowMode showMode = ShowMode.Delayed)
        {
            model.Setup(this);
            return base.SetupAndShow(model, showMode) as GenericInfoModalController;
        }

        protected override void Subscribe()
        {
            base.Subscribe();

            View.ActionButtonClicked += ActionButtonHandler;
            View.InfoButtonClicked += InfoButtonClickedHandler;
        }

        protected override void Unsubscribe()
        {
            base.Unsubscribe();

            View.ActionButtonClicked -= ActionButtonHandler;
            View.InfoButtonClicked -= InfoButtonClickedHandler;
        }

        private void ActionButtonHandler()
        {
            Model.PrimaryButtonHandler();

            // primary button call can have callbacks triggering hiding of the modal which can lead to NRE
            if (Model is { CloseOnPrimaryButton: true })
            {
                OnCloseButtonClicked();
            }
        }

        private void InfoButtonClickedHandler()
        {
            Model.InfoButtonHandler();
            HideModal();
        }

        void IModalControllerFacade.ShowFloatingText(string textId, params object[] args)
        {
            _overlayDialogConfig.DisplayType = DisplayType.FloatingText;
            _overlayDialogConfig.TargetTransform = View.FloatingTextAnchor;
            _overlayDialogConfig.ShowBackground = true;
            _overlayDialogConfig.TextToDisplay = textId;
            _overlayDialogConfig.TextArgs = new object[] { args };
            _overlayDialogManager.ShowOverlayDialog(_overlayDialogConfig);
        }

        void IModalControllerFacade.Show(GenericInfoModel model, ShowMode showMode)
        {
            SetupAndShow(model, showMode);
        }

        public void TriggerHide()
        {
            HideModal();
        }
    }
}