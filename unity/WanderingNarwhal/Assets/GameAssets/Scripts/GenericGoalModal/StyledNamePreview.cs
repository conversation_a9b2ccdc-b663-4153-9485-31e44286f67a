using System;
using BBB.Audio;
using BBB.DI;
using BBB.ProfileCustomization;
using BBB.UI.Core;
using BebopBee.Core.Audio;
using Febucci.UI;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.Quests
{
    public class StyledNamePreview : ContextedUiBehaviour
    {
        [SerializeField] private TextAnimator_TMP _textAnimator;
        [SerializeField] private GameObject _selectedHolder;
        [SerializeField] private Button _button;

        public NameStyle ItemUid { get; private set; }
        
        private NameStyleManager _nameStyleManager;
        protected override void InitWithContextInternal(IContext context)
        {
            _nameStyleManager = context.Resolve<NameStyleManager>();
        }
        
        public void Setup(string playerName, NameStyle nameStyle, Action<string> selectedCallback)
        {
            LazyInit();

            ItemUid = nameStyle;
            SetAsSelected(false);
            var styledText = _nameStyleManager.ApplyNameStyle(playerName, nameStyle);
            if (_textAnimator != null)
            {
                _textAnimator.SetText(styledText);
            }
        }

        private void ManualStop()
        {
            if (_textAnimator != null)
            {
                _textAnimator.ResetState();
            }
        }
        
        public void SetAsSelected(bool selected)
        {
            if (_selectedHolder != null)
            {
                AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
                _selectedHolder.SetActive(selected);
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            ManualStop();
        }
    }
}