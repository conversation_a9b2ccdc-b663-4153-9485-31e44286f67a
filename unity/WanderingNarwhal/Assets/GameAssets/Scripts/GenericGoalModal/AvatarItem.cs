using System;
using BBB.Audio;
using BBB.DI;
using BBB.UI.Core;
using BebopBee.Core.Audio;
using UnityEngine;
using UnityEngine.UI;
using BBB.AvatarCustomization.UI;
using BebopBee;
using TMPro;

namespace BBB.Quests
{
    public class AvatarItem : ContextedUiBehaviour
    {
        [SerializeField] private AsyncImage _avatarImage;
        [SerializeField] private GameObject _selectedHolder;
        [SerializeField] private Button _button;
        [SerializeField] private AsyncLoadablePrefab _framePrefab;

        public string ItemUid { get; private set; }
        private IAccountManager _accountManager;
        private Action<string> _selectedCallback;
        private AvatarFrameView _avatarFrameView;
        private string _cachedItemUid;

        protected override void InitWithContextInternal(IContext context)
        {
            _accountManager = context.Resolve<IAccountManager>();
            _button.ReplaceOnClick(() => _selectedCallback?.Invoke(ItemUid));
        }

        public void Setup(string itemUid, Action<string> selectedCallback)
        {
            SetupAsAvatar(itemUid, selectedCallback);
        }

        public void SetupAsAvatar(string itemUid, Action<string> selectedCallback)
        {
            LazyInit();

            ItemUid = _cachedItemUid = itemUid;
            _selectedCallback = selectedCallback;

            SetAsSelected(false);
            
            if (_framePrefab != null)
            {
                _framePrefab.Show(_accountManager.Profile.AvatarFrame, OnFrameCreated);
            }
        }

        public void SetupAsFrame(string frameUid, Action<string> selectedCallback)
        {
            LazyInit();

            ItemUid = frameUid;
            _cachedItemUid = _accountManager.Profile.Avatar;
            _selectedCallback = selectedCallback;

            SetAsSelected(false);

            if (_framePrefab != null && !frameUid.IsNullOrEmpty())
            {
                _framePrefab.Show(frameUid, OnFrameCreated);
            }
        }

        private void OnFrameCreated(GameObject frameObject)
        {
            _avatarFrameView = frameObject.GetComponent<AvatarFrameView>();
            if (_avatarFrameView != null)
            {
                _avatarFrameView.SetupAvatar(_cachedItemUid);
            }
        }

        public void SetupAsBadge(string badgeUid, Action<string> selectedCallback)
        {
            LazyInit();

            ItemUid = badgeUid;
            _selectedCallback = selectedCallback;

            SetAsSelected(false);
            _avatarImage.Load(badgeUid, "badges", error =>
            {
                Debug.LogWarning($"Failed to load badge image for {badgeUid}: {error}");
            });
        }

        public void ManualStop()
        {
            if (_framePrefab != null)
            {
                if (_avatarFrameView != null)
                {
                    _avatarFrameView.Clear();
                    _avatarFrameView = null;
                }
                _framePrefab.Clear();
            }
        }

        public void SetAsSelected(bool selected)
        {
            if (_selectedHolder != null)
            {
                AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
                _selectedHolder.SetActive(selected);
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            ManualStop();
        }
    }
}