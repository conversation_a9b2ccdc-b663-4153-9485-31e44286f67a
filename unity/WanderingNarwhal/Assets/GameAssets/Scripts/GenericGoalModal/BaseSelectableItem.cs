using System;
using BBB.Audio;
using BBB.DI;
using BBB.UI.Core;
using BebopBee.Core.Audio;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.Quests
{
    /// <summary>
    /// Base class for selectable UI items that can be clicked and show selection state
    /// </summary>
    public abstract class BaseSelectableItem : ContextedUiBehaviour
    {
        [SerializeField] protected GameObject _selectedHolder;
        [SerializeField] protected Button _button;

        /// <summary>
        /// The unique identifier for this item
        /// </summary>
        public abstract string ItemUid { get; protected set; }

        /// <summary>
        /// Callback invoked when this item is selected
        /// </summary>
        protected Action<string> _selectedCallback;

        protected override void InitWithContextInternal(IContext context)
        {
            InitializeWithContext(context);
            
            if (_button != null)
            {
                _button.ReplaceOnClick(() => _selectedCallback?.Invoke(ItemUid));
            }
        }

        /// <summary>
        /// Override this method to perform additional context initialization in derived classes
        /// </summary>
        /// <param name="context">The dependency injection context</param>
        protected virtual void InitializeWithContext(IContext context)
        {
            // Default implementation does nothing - override in derived classes as needed
        }

        /// <summary>
        /// Sets the visual selection state of this item
        /// </summary>
        /// <param name="selected">True to show as selected, false to show as unselected</param>
        public virtual void SetAsSelected(bool selected)
        {
            if (_selectedHolder != null)
            {
                AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
                _selectedHolder.SetActive(selected);
            }
        }

        /// <summary>
        /// Sets up the item with the given parameters
        /// </summary>
        /// <param name="itemUid">The unique identifier for this item</param>
        /// <param name="selectedCallback">Callback to invoke when item is selected</param>
        public virtual void Setup(string itemUid, Action<string> selectedCallback)
        {
            LazyInit();
            
            ItemUid = itemUid;
            _selectedCallback = selectedCallback;
            SetAsSelected(false);
            
            PerformSpecificSetup(itemUid, selectedCallback);
        }

        /// <summary>
        /// Override this method to perform item-specific setup logic in derived classes
        /// </summary>
        /// <param name="itemUid">The unique identifier for this item</param>
        /// <param name="selectedCallback">Callback to invoke when item is selected</param>
        protected abstract void PerformSpecificSetup(string itemUid, Action<string> selectedCallback);

        /// <summary>
        /// Override this method to perform cleanup logic in derived classes
        /// </summary>
        protected virtual void PerformCleanup()
        {
            // Default implementation does nothing - override in derived classes as needed
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            PerformCleanup();
        }
    }
}
