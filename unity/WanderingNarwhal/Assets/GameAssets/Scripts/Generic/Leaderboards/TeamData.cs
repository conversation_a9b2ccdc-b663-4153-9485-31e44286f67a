using System.Collections.Generic;
using GameAssets.Scripts.SocialScreens.Teams.TeamInfo;

namespace BBB
{
    /// <summary>
    /// Local replacement for RPC.Teams.TeamData
    /// </summary>
    public class TeamData
    {
        public string TeamUid { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Icon { get; set; }
        public List<TeamMemberData> Members { get; set; }
        public TeamType TeamType { get; set; }
        public int Score { get; set; }
        public int Activity { get; set; }
        public long JoinedAt { get; set; }
        public string Country { get; set; }
        public int RequiredLevel { get; set; }
    }

}
