using BebopBee;

namespace BBB
{
    public class LeaderboardItem
    {
        public string Uid { get; }
        public string Avatar { get; set; }
        public string Name { get; set; }
        public int Score { get; set; }
        public int Rank { get; set; }
        public string AvatarFrame { get; set; }
        public string BadgeUid { get; set; }

        public LeaderboardItem(string avatar, int score, string name, string uid, string avatarFrame, string badgeUid)
        {
            Avatar = avatar;
            Score = score;
            Name = name;
            Uid = uid;
            AvatarFrame = avatarFrame;
            BadgeUid = badgeUid;
        }

        public bool IsLocalPlayer(IAccountManager accountManager)
        {
            return Uid == accountManager.Profile.Uid;
        }
    }
}