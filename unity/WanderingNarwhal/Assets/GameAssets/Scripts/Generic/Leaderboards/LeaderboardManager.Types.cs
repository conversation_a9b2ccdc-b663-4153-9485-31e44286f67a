using System;
using System.Collections.Generic;
using System.Text;
using Newtonsoft.Json;

namespace BBB
{
    public partial class LeaderboardManager
    {
        public struct Score
        {
            public string Uid;
            public string Name;
            public string Country;
            public int Trophies;
            public string Avatar;
            public int Position;
            public string LastLocation;
            public string LastLevel;
            public bool IsOwnPlayer;
            public string AvatarFrame;
            public string BadgeUid;
        }

        public class FilteredScoresData
        {
            [JsonProperty("Scores")]
            private readonly List<Score> _scores = new();
            [JsonProperty("Scores Indicies")]
            private readonly Dictionary<string, int> _scoreIndexes = new();
            public int UserPosition = -1;
            public int UserTrophies = -1;
            public bool Loading;

            public FilteredScoresData Clone()
            {
                var clonedData = new FilteredScoresData();
                clonedData._scores.AddRange(_scores);
                foreach (var kvp in _scoreIndexes)
                    clonedData._scoreIndexes.Add(kvp.Key, kvp.Value);
                clonedData.UserPosition = UserPosition;
                clonedData.UserTrophies = UserTrophies;
                clonedData.Loading = false;
                return clonedData;
            }

            public int ScoresCount()
            {
                return _scores.Count;
            }

            public bool IsOnlyOwn()
            {
                return _scores.Count == 1 && _scores[0].IsOwnPlayer;
            }

            public Score this[int i] => _scores[i];

            public void Replace(Score score, int index)
            {
                _scores[index] = score;
                _scoreIndexes[score.Uid] = index;
            }

            public void Add(Score score)
            {
                if (_scoreIndexes.ContainsKey(score.Uid))
                    return;

                _scores.Add(score);
                _scoreIndexes.Add(score.Uid, _scores.Count - 1);
                
                if (!score.IsOwnPlayer) return;
                
                UserPosition = score.Position;
                UserTrophies = score.Trophies;
            }

            public void Clear()
            {
                _scores.Clear();
                _scoreIndexes.Clear();
            }
            
            public override string ToString()
            {
                var sb = new StringBuilder();
                sb.Append("Score count ").Append(_scores.Count)
                    .Append(" UserPosition: ").Append(UserPosition)
                    .Append(" UserTrophies: ").Append(UserTrophies)
                    .Append(" Loading: ").Append(Loading);

                return sb.ToString();
            }
            
            public void SortScores(Comparison<Score> func)
            {
                _scores.Sort(func);
                _scoreIndexes.Clear();
                for (var i = 0; i < _scores.Count; i++)
                {
                    _scoreIndexes.Add(_scores[i].Uid, i);
                }
            }

            public List<Score> GetScores()
            {
                return _scores;
            }

            public Score GetTrophies(string uid)
            {
                return _scoreIndexes.TryGetValue(uid, out var foundIndex) ? _scores[foundIndex] : default;
            }

            public Score? FindPlayerInScoresWithIndex(string uid, out int index)
            {
                if (_scoreIndexes.TryGetValue(uid, out var foundIndex))
                {
                    index = foundIndex;
                    return _scores[index];
                }

                index = -1;
                return null;
            }

            public void FindOwnScore(out int index)
            {
                index = -1;
                foreach (var score in _scores)
                {
                    if (!score.IsOwnPlayer)
                        continue;

                    _scoreIndexes.TryGetValue(score.Uid, out index);
                    return;
                }
            }

            public void MoveFromTo(int fromIndex, int toIndex)
            {
                var score = _scores[fromIndex];
                _scores.RemoveAt(fromIndex);
                _scores.SafeInsert(toIndex, score);
                _scoreIndexes[score.Uid] = toIndex;
            }
        }
    }
}