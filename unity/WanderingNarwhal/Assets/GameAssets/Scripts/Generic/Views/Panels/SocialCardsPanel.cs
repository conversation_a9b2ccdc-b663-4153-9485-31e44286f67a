using System;
using System.Collections;
using System.Collections.Generic;
using BBB;
using BBB.Audio;
using BBB.Core;
using BBB.DI;
using BBB.Tools;
using BBB.UI.Level;
using BebopBee;
using BebopBee.Core;
using BebopBee.Core.Audio;
using BebopBee.Social;
using CustomLayout;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using GameAssets.Scripts.SocialScreens.Teams;
using GameAssets.Scripts.Utils;
using UnityEngine;
using UnityEngine.UI;

public class SocialCardsPanel : BbbMonoBehaviour
{
    [Header("Animations Settings")] [SerializeField]
    private float _timeBeforeStartFriendsCardsMovement = 2f;

    [SerializeField] private float _cardsListMoveTimeIntro = 5f;
    [SerializeField] private Ease _cardsIntroEase = Ease.OutCubic;
    [SerializeField] private float _cardsListMoveTimeOutro = 2f;
    [SerializeField] private float _cardsListMoveOffsetOutro = 20f;
    [SerializeField] private Ease _cardsOutroEase = Ease.OutCubic;
    [SerializeField] private float _pauseBetweenIntroAndOutro = 1f;

    [Header("References")] [SerializeField]
    private RectTransform _holdersRoomTransform;

    [SerializeField] private RectTransform _leftCardHolder;
    [SerializeField] private RectTransform _rightCardHolder;
    [SerializeField] private ProfileCardWidget _ownProfileCard;
    [SerializeField] private GameObject _friendCardPrefab;
    [SerializeField] private List<CustomLayoutGroup> _layoutGroups;
    [SerializeField] private ScrollRect _scrollRect;
    [SerializeField] private RectTransform _contentTransform;

    private ICoroutineExecutor _coroutineExecutor;

    private RectTransform _scrollRectTransform;
    private RectTransform _ownProfileCardTransform;
    private float _friendCardPrefabWidth;

    private readonly int _outroTriggerHash = Animator.StringToHash("Outro");

    private readonly Stack<ProfileCardWidget> _profileCardsPool = new Stack<ProfileCardWidget>();
    private readonly Stack<ProfileCardWidget> _activeProfileCards = new Stack<ProfileCardWidget>();

    private float _contentInitialPosX;
    private int _playSoundsFor = 0;
    private Coroutine _coroutine;
    private bool _noItems;
    private IAccountManager _accountManager;
    private bool _skipped;
    private Tweener _tweenerBeforeCardsMovement;
    private Tween _leftCardsTweener;
    private Tween _rightCardsTweener;

    public void Init(IContext context)
    {
        _coroutineExecutor = context.Resolve<ICoroutineExecutor>();
        _accountManager = context.Resolve<IAccountManager>();
    }

    public void PrewarmPool(int number)
    {
        for (var i = 0; i < number; i++)
        {
            var card = CreateCard(_leftCardHolder);
            card.gameObject.SetActive(false);
            _profileCardsPool.Push(card);
        }
    }

    public void Skip()
    {
        _tweenerBeforeCardsMovement?.Kill();
        _tweenerBeforeCardsMovement = null;
        _rightCardsTweener?.Kill();
        _rightCardsTweener = null;
        _leftCardsTweener?.Kill();
        _leftCardsTweener = null;
        _skipped = true;
    }

    public void Setup(Match3ResourceProvider resourceProvider, List<LeaderboardItem> items, int ownScore)
    {
        _skipped = false;

        var ownUid = _accountManager.Profile?.Uid;

        _scrollRectTransform = _scrollRect.GetComponent<RectTransform>();
        _friendCardPrefabWidth = _friendCardPrefab.GetComponent<RectTransform>().sizeDelta.x;
        _ownProfileCardTransform = _ownProfileCard.GetComponent<RectTransform>();

        var workingTransform = _leftCardHolder;
        int workingSiblingIndex = 0;

        if (ownUid == null)
            Debug.LogError("[SocialCardsPanel] No uid for local player found");

        for (int i = 0; i < items.Count; i++)
        {
            var item = items[i];
            if (ownUid != null && item.Uid == ownUid)
            {
                _ownProfileCard.Setup(resourceProvider, item, true, ownScore).Forget();
                workingTransform = _rightCardHolder;
                _playSoundsFor = items.Count - (i + 1);
                workingSiblingIndex = 0;
            }
            else
            {
                var friendCard = _profileCardsPool.Count > 0 ? _profileCardsPool.Pop() : CreateCard(workingTransform);
                friendCard.transform.SetParent(workingTransform);
                friendCard.transform.SetSiblingIndex(workingSiblingIndex);

                friendCard.Setup(resourceProvider, item, false).Forget();
                friendCard.gameObject.SetActive(true);

                _activeProfileCards.Push(friendCard);

                workingSiblingIndex++;
            }
        }

        _noItems = items.Count == 0;

        if (_noItems)
        {
            var item = new LeaderboardItem(GameConstants.FennecAvatarUrl, ownScore, "Player", string.Empty,
                ProfileUtils.DefaultFrameUid, string.Empty)
                {
                    Rank = 1
                };
            _ownProfileCard.Setup(resourceProvider, item, true, ownScore).Forget();
        }
    }

    private ProfileCardWidget CreateCard(Transform parent)
    {
        return ObjectUtils.Instantiate<ProfileCardWidget>(_friendCardPrefab, parent, false);
    }

    public void StartAnimate(Action onCompleted)
    {
        if (_noItems)
        {
            _ownProfileCard.gameObject.SetActive(true);
            _ownProfileCard.SetAnimatorTrigger(_outroTriggerHash);
            _scrollRect.enabled = false;
            return;
        }

        if (_layoutGroups.Count == 0)
            throw new Exception("No layout groups specified");

        foreach (var layoutGroup in _layoutGroups)
            layoutGroup.RefreshEverything();

        _ownProfileCard.gameObject.SetActive(true);

        _tweenerBeforeCardsMovement = Rx.Invoke(_timeBeforeStartFriendsCardsMovement, _ =>
        {
            _tweenerBeforeCardsMovement = null;

            if (_skipped)
                return;

            //AudioProxy.PlaySound(Match3SoundIds.SocialCardsMovementLoop, true);
            _coroutine = _coroutineExecutor.StartCoroutine(PlayCardMovementSound());

            _leftCardsTweener = TweenHolder(_leftCardHolder, -1, () =>
                {
                    if (_skipped)
                        return;

                    _ownProfileCard.SetAnimatorTrigger(_outroTriggerHash);
                    foreach (var card in _activeProfileCards)
                        card.SetAnimatorTrigger(_outroTriggerHash);

                    //AudioProxy.StopSound(Match3SoundIds.SocialCardsMovementLoop);
                },
                () =>
                {
                    if (_skipped)
                        return;


                    HandleScrollRectTransform();
                    HandleScrollRectContentTransform();
                    _scrollRect.enabled = true;
                    onCompleted.SafeInvoke();
                });

            _rightCardsTweener = TweenHolder(_rightCardHolder, 1);

            _leftCardsTweener?.OnComplete(() => _leftCardsTweener = null);
            _rightCardsTweener?.OnComplete(() => _rightCardsTweener = null);
        });
    }

    private IEnumerator PlayCardMovementSound()
    {
        for (int i = 0; i < _playSoundsFor; i++)
        {
            yield return WaitCache.Seconds(_cardsListMoveTimeIntro / _playSoundsFor);

            if (_skipped)
            {
                _coroutine = null;
                yield break;
            }

            AudioProxy.PlaySound(Match3SoundIds.SocialCardsMovementLoop);
        }

        yield return null;
    }


    private void HandleScrollRectTransform()
    {
        var tf = _scrollRect.GetComponent<RectTransform>();
        var sd = tf.sizeDelta;
        var pos = tf.anchoredPosition;

        var ownProfileCardRect = _ownProfileCardTransform.rect;
        var leftSideX = ownProfileCardRect.xMin;
        var rightSideX = ownProfileCardRect.xMax;

        sd.x = rightSideX - leftSideX;
        pos.x = leftSideX + sd.x / 2f;

        tf.sizeDelta = sd;
        tf.anchoredPosition = pos;
    }

    private void HandleScrollRectContentTransform()
    {
        var sd = _contentTransform.sizeDelta;

        sd.x = _ownProfileCardTransform.sizeDelta.x
               + 2 * _cardsListMoveOffsetOutro
               + _leftCardHolder.sizeDelta.x
               + _rightCardHolder.sizeDelta.x;

        _contentTransform.sizeDelta = sd;

        var contentPosX = (_leftCardHolder.rect.xMin + _rightCardHolder.rect.xMax) / 2f -
                          _scrollRectTransform.anchoredPosition.x;

        var contentPos = _contentTransform.anchoredPosition;
        contentPos.x = contentPosX;
        _contentTransform.anchoredPosition = contentPos;

        _contentInitialPosX = contentPosX;
    }

    private Sequence TweenHolder(RectTransform holder, int outroModifier, Action introCallback = null,
        Action outroCallback = null)
    {
        var x = _rightCardHolder.sizeDelta.x + 4 * _friendCardPrefabWidth;
        var translationVec = new Vector3(-x, 0f, 0f);
        holder.Translate(translationVec);

        holder.gameObject.SetActive(true);

        var introTweener = holder.DOLocalMoveX(0f, _cardsListMoveTimeIntro)
            .SetEase(_cardsIntroEase).OnComplete(introCallback.SafeInvoke);

        var ownCardWidth = _ownProfileCardTransform == null ? 0f : _ownProfileCardTransform.sizeDelta.x;

        var outroTarget = _cardsListMoveOffsetOutro + ownCardWidth / 2f;
        var outroTweener = holder.DOLocalMoveX(outroModifier * outroTarget, _cardsListMoveTimeOutro)
            .SetEase(_cardsOutroEase).OnComplete(outroCallback.SafeInvoke);

        var sequence = DOTween.Sequence();
        sequence.Append(introTweener);
        sequence.AppendInterval(_pauseBetweenIntroAndOutro);
        sequence.Append(outroTweener);

        return sequence;
    }

    public void Clear()
    {
        while (_activeProfileCards.Count > 0)
        {
            var profileCard = _activeProfileCards.Pop();
            profileCard.Clear();
            profileCard.transform.SetParent(_leftCardHolder);
            profileCard.gameObject.SetActive(false);
            _profileCardsPool.Push(profileCard);
        }

        _ownProfileCard.gameObject.SetActive(false);
        _ownProfileCard.Clear();

        _leftCardHolder.localPosition = Vector3.zero;
        _leftCardHolder.sizeDelta = Vector2.zero;
        _leftCardHolder.gameObject.SetActive(false);
        _rightCardHolder.localPosition = Vector3.zero;
        _rightCardHolder.sizeDelta = Vector2.zero;
        _rightCardHolder.gameObject.SetActive(false);

        if (_coroutine != null)
        {
            _coroutineExecutor.StopCoroutine(_coroutine);
            _coroutine = null;
        }

        _scrollRect.enabled = false;
        _scrollRect.horizontalNormalizedPosition = 0f;
        if(_ownProfileCardTransform != null)
            _ownProfileCardTransform.anchoredPosition = new Vector2(0f, _ownProfileCardTransform.anchoredPosition.y);
        _holdersRoomTransform.anchoredPosition = new Vector2(0f, _holdersRoomTransform.anchoredPosition.y);
        if(_contentTransform != null && _scrollRectTransform != null)
            _contentTransform.sizeDelta = new Vector2(_scrollRectTransform.sizeDelta.x, _contentTransform.sizeDelta.y);
    }

    private void Update()
    {
        if (_scrollRect.enabled)
        {
            var posX = _contentTransform.anchoredPosition.x - _contentInitialPosX;
            _ownProfileCardTransform.anchoredPosition = new Vector2(posX, _ownProfileCardTransform.anchoredPosition.y);
            _holdersRoomTransform.anchoredPosition = new Vector2(posX, _holdersRoomTransform.anchoredPosition.y);
        }
    }
}