using System;
using System.Collections.Generic;
using BBB.DI;
using BBB.DI.Extensions;
using BBB.Match3.Renderer;

namespace BBB.UI
{
    public class Match3LevelContext : UnityContext
    {
        public Match3LevelContext() : base()
        {
            CopyResolvers = true;
        }

        protected override void OnRegisterContext()
        {
            base.OnRegisterContext();

            RegisterType<ITileLayerView, ITileLayer>(layer =>
            {
                switch (layer.State)
                {
                    case TileLayerState.Normal:
                        return new NormalTileLayerView(layer);
                    case TileLayerState.Blinking:
                        return new BlinkingTileLayerView(layer);
                    case TileLayerState.HorizontalLb:
                    case TileLayerState.VerticalLb:
                    case TileLayerState.Bomb:
                    case TileLayerState.Propeller:
                        return new SpecialTileLayerView(layer);
                    case TileLayerState.Sticker:
                        return new StickerLayerView(layer);
                    case TileLayerState.Frame:
                        return new FrameLayerView(layer);
                    case TileLayerState.Animal:
                        return new AnimalLayerView(layer);
                    case TileLayerState.IceCube:
                        return new IceCubeLayerView(layer);
                    case TileLayerState.Vase:
                        return new VaseLayerView(layer);
                    case TileLayerState.MoneyBag:
                        return new MoneyBagLayerView(layer);
                    case TileLayerState.Penguin:
                        return new PenguinLayerView(layer);
                    case TileLayerState.DropItem:
                        return new DropItemLayerView(layer);
                    case TileLayerState.ColorBomb:
                        return new ColorBombLayerView(layer);
                    case TileLayerState.Litter:
                        return new LitterLayerView(layer);
                    case TileLayerState.Chained:
                        return new ChainTileLayerView(layer);
                    case TileLayerState.Sand:
                        return new SandLayerView(layer);
                    case TileLayerState.Pinata:
                        return new PinataLayerView(layer);
                    case TileLayerState.ColorCrate:
                        return new ColorCrateLayerView(layer);
                    case TileLayerState.Watermelon:
                        return new WatermelonLayerView(layer);
                    case TileLayerState.Egg:
                        return new EggLayerView(layer);
                    case TileLayerState.Bird:
                        return new BirdLayerView(layer);
                    case TileLayerState.Sheep:
                        return new SheepLayerView(layer);
                    case TileLayerState.Banana:
                        return new BananaLayerView(layer);
                    case TileLayerState.Monkey:
                        return new MonkeyLayerView(layer);
                    case TileLayerState.Skunk:
                        return new SkunkLayerView(layer);
                    case TileLayerState.GameEventLabel:
                        return new GameEventLabelLayerView(layer);
                    case TileLayerState.Hen:
                        return new HenLayerView(layer);
                    case TileLayerState.Chicken:
                        return new ChickenLayerView(layer);
                    case TileLayerState.Hive:
                        return new HiveLayerView(layer);
                    case TileLayerState.Bee:
                        return new BeeLayerView(layer);
                    case TileLayerState.Mole:
                        return new MoleLayerView(layer);
                    case TileLayerState.Squid:
                        return new SquidLayerView(layer);
                    case TileLayerState.StealingHatLabel:
                        return new DidiHatLayerView(layer);
                    case TileLayerState.Toad:
                        return new ToadLayerView(layer);
                    case TileLayerState.MagicHat:
                        return new MagicHatLayerView(layer);
                    case TileLayerState.Bowling:
                        return new BowlingLayerView(layer);
                    case TileLayerState.Bush:
                        return new BushLayerView(layer);
                    case TileLayerState.Soda:
                        return new SodaLayerView(layer);
                    case TileLayerState.Safe:
                        return new SafeLayerView(layer);
                    case TileLayerState.FlowerPot:
                        return new FlowerPotLayerView(layer);
                    case TileLayerState.IceBar:
                        return new IceBarLayerView(layer);
                    case TileLayerState.DynamiteBox:
                        return new DynamiteBoxLayerView(layer);
                    case TileLayerState.GiantPinata:
                        return new GiantPinataLayerView(layer);
                    case TileLayerState.MetalBar:
                        return new MetalBarLayerView(layer);
                    case TileLayerState.Shelf:
                        return new ShelfLayerView(layer);
                    case TileLayerState.JellyFish:
                        return new JellyFishLayerView(layer);
                    case TileLayerState.GoldenScarab:
                        return new GoldenScarabLayerView(layer);
                    case TileLayerState.Gondola:
                        return new GondolaLayerView(layer);
                    case TileLayerState.TukTuk:
                        return new TukTukLayerView(layer);
                    case TileLayerState.FireWorks:
                        return new FireWorksLayerView(layer);
                    case TileLayerState.SlotMachine:
                        return new SlotMachineLayerView(layer);
                    default:
                        throw new ArgumentOutOfRangeException();
                }
            });

            RegisterInstance<IList<ICellLayer>>(new ContextedList<ICellLayer>
            {
                new NormalCellLayer(),
                new WallLeftCellLayer(),
                new WallTopCellLayer(),
                new WallRightCellLayer(),
                new WallBottomCellLayer(),
                new BackgroundOneCellLayer(),
                new BackgroundDoubleCellLayer(),
                new DespawnerCellLayer(),
                new IvyCellLayer(),
                new TntCellLayer(),
                new PetalCellLayer(),
                new WaterCellLayer(),
                new FlagEndCellLayer(),
#if BBB_DEBUG
                new AssistMarkerCellLayer(),
#endif
            });

            RegisterInstance<IList<ITileLayer>>(new ContextedList<ITileLayer>
            {
                new NormalTileLayer(),
                new HorizontalBreakerTileLayer(),
                new VerticalBreakerTileLayer(),
                new BombTileLayer(),
                new ColorBombTileLayer(),
                new StickerTileLayer(),
                new FrameTileLayer(),
                new DropItemTileLayer(),
                new LitterItemTileLayer(),
                new ChainItemTileLayer(),
                new BlinkingTileLayer(),
                new PinataTileLayer(),
                new AnimalTileLayer(),
                new IceCubeTileLayer(),
                new SandTileLayer(),
                new ColorCrateTileLayer(),
                new WatermelonTileLayer(),
                new VaseTileLayer(),
                new MoneyBagTileLayer(),
                new PenguinTileLayer(),
                new EggTileLayer(),
                new BirdTileLayer(),
                new SheepTileLayer(),
                new BananaTileLayer(),
                new MonkeyTileLayer(),
                new SkunkTileLayer(),
                new GameEventLabelTileLayer(),
                new HenTileLayer(),
                new ChickenTileLayer(),
                new HiveTileLayer(),
                new BeeTileLayer(),
                new MoleTileLayer(),
                new SquidTileLayer(),
                new DidiHatLabelTileLayer(),
                new ToadTileLayer(),
                new MagicHatTileLayer(),
                new PropellerTileLayer(),
                new BowlingTileLayer(),
                new BushTileLayer(),
                new SodaTileLayer(),
                new SafeTileLayer(),
                new FlowerPotTileLayer(),
                new IceBarTileLayer(),
                new DynamiteBoxTileLayer(),
                new GiantPinataTileLayer(),
                new MetalBarTileLayer(),
                new ShelfTileLayer(),
                new JellyFishTileLayer(),
                new GoldenScarabTileLayer(),
                new GondolaTileLayer(),
                new TukTukTileLayer(),
                new FireWorksTileLayer(),
                new SlotMachineTileLayer(),
            });
        }
    }
}
