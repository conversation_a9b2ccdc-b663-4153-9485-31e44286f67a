using System;
using BBB.Core.Analytics;
using BBB.DailyLogin;
using BBB.DI;

namespace BBB.UI
{
    public class DailyLoginHud : GenericHudComponent
    {
        private DailyLoginManager _dailyLoginManager;

        protected override bool ConnectionDependent => true;
        protected override INotifierStatus Notifier => NotificationManager.GetDailyLoginNotifier();
        protected override bool ShouldShowTimer { get; set; } = true;
        protected override bool ShouldShowDebugButton => false;
        
        protected override void OnInit(IContext context)
        {
            _dailyLoginManager = context.Resolve<DailyLoginManager>();
        }

        protected override void ButtonAction()
        {
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnHud.Name, DauInteractions.TapOnHud.DailyLogin, string.Empty));
            _dailyLoginManager.TryShowDailyLoginModal(FloatingTextAnchor);
        }

        protected override TimeSpan GetRemainingTime(string _)
        {
            return _dailyLoginManager.GameEvent.GetTimeLeft();
        }

        protected override bool ShouldBeShown()
        {
            return _dailyLoginManager.ShouldShowHud();
        }

        protected override void OnShow()
        {
            _dailyLoginManager.UpdateGameEvent();
        }

        protected override void OnHide()
        {
        }

        protected override void Subscribe()
        {
            base.Subscribe();
            _dailyLoginManager.CurrentDayClaimed += RefreshVisibility;
            _dailyLoginManager.GameEventEnded += RefreshVisibility;
        }

        protected override void Unsubscribe()
        {
            base.Unsubscribe();

            if (_dailyLoginManager != null)
            {
                _dailyLoginManager.CurrentDayClaimed -= RefreshVisibility;
                _dailyLoginManager.GameEventEnded -= RefreshVisibility;
            }
        }
    }
}