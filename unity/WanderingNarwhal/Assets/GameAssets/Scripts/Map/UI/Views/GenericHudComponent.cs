using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.UI.Core;
using BBB.Wallet;
using BebopBee;
using BebopBee.Core;
using BebopBee.UnityEngineExtensions;
using DG.Tweening;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Map.UI.Controllers;
using GameAssets.Scripts.UI.OverlayDialog;
using JetBrains.Annotations;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Button = UnityEngine.UI.Button;

namespace BBB.UI
{
    [RequireComponent(typeof(Animator), typeof(GraphicRaycaster))]
    public abstract class GenericHudComponent : BbbMonoBehaviour
    {
        protected const string OpenLocUid = "OPEN_LOC";

        private static readonly Vector3 LeftHudScale = new(-1f, 1f, 1f);
        private static readonly Vector3 RightHudScale = new(1f, 1f, 1f);

        private static readonly int Hide = Animator.StringToHash("Hide");
        private static readonly int Show = Animator.StringToHash("Show");
        private static readonly int Highlight = Animator.StringToHash("Highlight");

        [SerializeField] protected Animator _animator;
        [SerializeField] private GraphicRaycaster _graphicRaycaster;
        [SerializeField] private Transform _currencyWaypointsRoot;
        [SerializeField] private Button _actionButton;
        [SerializeField] private Button _debugButton;
        [Header("Notifier")]
        [SerializeField] private GameObject _notifierHolder;
        [SerializeField] private NotifierWidget _notifierWidget;

        [Header("IncrementText")]
        [SerializeField] protected Animator _fxAnimator;
        [SerializeField] private TextMeshProUGUI _deltaText;
        [SerializeField] private Transform _deltaTextRoot;
        [SerializeField] private Transform _leftSideDeltaTextAnchor;
        [SerializeField] private Transform _rightSideDeltaTextAnchor;
        [SerializeField] private string _deltaMaskStringFormatMask = "+{0}";

        [SerializeField] private GameObject _textHolder;
        [SerializeField] private LocalizedTextPro _text;

        [SerializeField] private GameObject _clockCountdownHolder;
        [SerializeField] private ClockCountdownText _clockCountdown;

        [field: SerializeField] protected Transform FloatingTextAnchor { get; private set; }
        [field: SerializeField] protected float MaxHighlightTime { get; private set; } = 2.2f;
        [field: SerializeField] public string SlotUid { get; protected set; }

        private HudSlotsManager _hudSlotsManager;
        private ILocalizationManager _localizationManager;
        private IScreensManager _screensManager;
        private IUIWalletManager _uiWalletManager;
        private IOverlayDialogManager _overlayDialogManager;

        private bool _isShown;
        private bool _currencyAnimationInProcess;
        private bool _interrupted;
        private readonly OverlayDialogConfig _overlayDialogConfig = new();
        private readonly List<Tween> _safeCancelableTweeners = new();

        protected List<Tween> CancelableTweeners { get; } = new();

        protected IEventDispatcher EventDispatcher { get; private set; }
        protected GameNotificationManager NotificationManager { get; private set; }
        protected GenericHudManager GenericHudManager { get; private set; }

        protected abstract bool ConnectionDependent { get; }
        protected abstract INotifierStatus Notifier { get; }
        protected abstract bool ShouldShowTimer { get; set; }
        protected abstract bool ShouldShowDebugButton { get; }

        public bool ShownWithPriorityIgnore { get; set; }
        public virtual bool ShouldIgnoreSlotPriority() => false;
        protected abstract bool ShouldBeShown();
        protected abstract void OnShow();
        protected abstract void OnHide();
        protected abstract void ButtonAction();
        protected virtual string CurrencyUid => string.Empty;
        protected virtual string HudBlockUid => string.Empty;

        protected virtual void DebugButtonHandler()
        {
        }

        protected virtual TimeSpan GetRemainingTime(string uid)
        {
            return TimeSpan.Zero;
        }

        public void Init(IContext context)
        {
            GenericHudManager = context.Resolve<GenericHudManager>();
            NotificationManager = context.Resolve<GameNotificationManager>();
            EventDispatcher = context.Resolve<IEventDispatcher>();

            _overlayDialogManager = context.Resolve<IOverlayDialogManager>();
            _uiWalletManager = context.Resolve<IUIWalletManager>();

            _hudSlotsManager = context.Resolve<HudSlotsManager>();
            _screensManager = context.Resolve<IScreensManager>();
            _localizationManager = context.Resolve<ILocalizationManager>();

            OnInit(context);

            if (_debugButton != null)
            {
                var showDebug = ShouldShowDebugButton && AppDefinesConverter.BbbDebug;
                if (showDebug)
                {
                    _debugButton.ReplaceOnClick(DebugButtonHandler);
                }

                _debugButton.gameObject.SetActive(showDebug);
            }

            _actionButton.ReplaceOnClick(ButtonHandler);

            SubscribeLifetime();
            ForceHide();
        }

        protected abstract void OnInit(IContext context);

        protected override void OnDestroy()
        {
            base.OnDestroy();

            if (_isShown)
            {
                ForceHide(true);
            }

            UnsubscribeLifetime();
            KillTweeners();
        }

        public void SetSide(Side side, RuntimeAnimatorController animatorController)
        {
            if (_currencyWaypointsRoot != null)
            {
                _currencyWaypointsRoot.localScale = side switch
                {
                    Side.Left => LeftHudScale,
                    Side.Right => RightHudScale,
                    _ => _currencyWaypointsRoot.localScale
                };
            }

            if (_animator.runtimeAnimatorController != animatorController)
            {
                _animator.enabled = false;
                _animator.runtimeAnimatorController = animatorController;
                _animator.Rebind();
                _animator.enabled = true;
            }

            if (_deltaTextRoot != null)
            {
                var incrementTextAnchor = side switch
                {
                    Side.Left => _leftSideDeltaTextAnchor,
                    Side.Right => _rightSideDeltaTextAnchor,
                    _ => throw new ArgumentOutOfRangeException(nameof(side), side, null)
                };

                _deltaTextRoot.position = incrementTextAnchor.position;
            }

            _animator.Update(0f);
        }

        public void TryToShow()
        {
            var shouldBeShown = this != null && !GenericHudManager.IsLocked && ShouldBeShownByConnection() && ShouldBeShown();
            if (!shouldBeShown)
            {
                if (_isShown)
                {
                    TryToHide();
                }

                return;
            }

            // if already shown, we do not need to trigger showing sequence, but view refresh still should happen,
            // as TryToShow calls happens on many dynamic subscriptions
            if (_isShown)
            {
                OnShowInternal();
                return;
            }

            if (!_hudSlotsManager.TryToShow(this))
                return;

            BDebug.LogFormat(LogCat.Hud, "Shown hud: {0}, {1}, {2}", gameObject.name, Time.frameCount, Time.time);
            _isShown = true;
            if (gameObject.activeSelf)
            {
                _animator.ResetAllParameters();
                _animator.SetTrigger(Show);
            }

            gameObject.SetActive(_isShown);
            _graphicRaycaster.enabled = _isShown;

            OnShowInternal();
            Subscribe();
        }

        protected void TryToHide()
        {
            if (_currencyAnimationInProcess)
                return;

            if (this != null && _isShown && gameObject.activeSelf)
            {
                DoHide(false, HideType.Hide);
            }
        }

        [UsedImplicitly]
        private void OutroAnimationCallback()
        {
            if (_isShown)
                return;

            DoHide(true, HideType.Hide);
        }

        public void ForceHide(bool destroying = false)
        {
            DoHide(true, destroying ? HideType.Destroy : HideType.ForceHide);
        }

        private void DoHide(bool immediate, HideType hideType)
        {
            _graphicRaycaster.enabled = false;
            _animator.ResetAllParameters();

            if (_isShown)
            {
                KillTweeners();
                Unsubscribe();
                OnHide();
                _isShown = false;

                if (!immediate)
                {
                    _animator.SetTrigger(Hide);
                }
            }

            if (immediate)
            {
                gameObject.SetActive(false);
                _hudSlotsManager.RefreshOnHide(this, hideType);
            }
        }

        protected virtual void SubscribeLifetime()
        {
            UnsubscribeLifetime();

            ConnectivityStatusManager.ConnectivityChanged += ConnectionStatusChangedHandler;
            GenericHudManager.HudShown += HudShownHandler;
        }

        protected virtual void UnsubscribeLifetime()
        {
            ConnectivityStatusManager.ConnectivityChanged -= ConnectionStatusChangedHandler;

            if (GenericHudManager != null)
            {
                GenericHudManager.HudShown -= HudShownHandler;
            }
        }

        protected virtual void Subscribe()
        {
            Unsubscribe();

            _screensManager.OnScreenChanged += ScreenChangedHandler;

            _uiWalletManager.CurrenciesVisualizationEndedEvent += CurrenciesVisualizationEndedHandler;
            _uiWalletManager.CurrenciesScheduledToVisualizeEvent += CurrenciesScheduledToVisualizeHandler;
            _uiWalletManager.CurrenciesVisualizationStartedEvent += AnimateDelta;

            EventDispatcher.AddListener<LevelRewardInterruptionEvent>(LevelRewardInterruptionEventHandler);
        }

        protected virtual void Unsubscribe()
        {
            if (_clockCountdown != null)
            {
                _clockCountdown.TimerExpired -= TimerExpiredHandler;
                _clockCountdown.Uninit();
            }

            if (_screensManager != null)
            {
                _screensManager.OnScreenChanged -= ScreenChangedHandler;
            }

            if (_uiWalletManager != null)
            {
                _uiWalletManager.CurrenciesScheduledToVisualizeEvent -= CurrenciesScheduledToVisualizeHandler;
                _uiWalletManager.CurrenciesVisualizationStartedEvent -= AnimateDelta;
                _uiWalletManager.CurrenciesVisualizationEndedEvent -= CurrenciesVisualizationEndedHandler;
            }

            EventDispatcher?.RemoveListener<LevelRewardInterruptionEvent>(LevelRewardInterruptionEventHandler);
        }

        private void CurrenciesVisualizationEndedHandler()
        {
            // generic refresh for side hud which doesn't handle currencies on its own
            if (CurrencyUid.IsNullOrEmpty())
            {
                RefreshVisibility();
            }
        }

        protected void RefreshVisibility()
        {
            if (GenericHudManager.IsHudVisible)
            {
                TryToShow();
            }
            else
            {
                TryToHide();
            }
        }

        protected void SetText(string localizedUid)
        {
            _text.SetTextId(localizedUid);
            ShowTimer(false);
        }

        protected void SetRawText(string rawText)
        {
            _text.SetRawText(rawText);
            ShowTimer(false);
        }

        private void OnShowInternal()
        {
            OnShow();
            RefreshNotifier();
            RefreshTimer();
        }

        private void ShowTimer(bool showTimer)
        {
            _clockCountdownHolder.SetActive(showTimer);
            _textHolder.SetActive(!showTimer);
        }

        private bool ShouldBeShownByConnection()
        {
            return ConnectivityStatusManager.ConnectivityReachable || !ConnectionDependent;
        }

        private void ConnectionStatusChangedHandler(bool reachable)
        {
            RefreshVisibility();
        }

        private void ScreenChangedHandler(ScreenType arg1, IScreensController arg2, IViewPresenter arg3)
        {
            _currencyAnimationInProcess = false;
            RefreshVisibility();
        }

        private void HudShownHandler(bool hudShown)
        {
            if (hudShown)
            {
                TryToShow();
            }
            else
            {
                TryToHide();
            }
        }

        private void ButtonHandler()
        {
            RefreshVisibility();
            if (!_isShown)
                return;

            if (_screensManager.GetCurrentController() is IFlowActionsScreenController mapController)
            {
                mapController.InterruptLevelRewarding(ButtonAction);
            }
            else
            {
                ButtonAction();
            }
        }

        protected void ShowOfflineText()
        {
            ShowFloatingText(LocalizationManagerHelper.OfflineConnectionProblemKey);
        }

        private void ShowFloatingText(string textId, DisplayType displayType = DisplayType.FloatingText, params object[] args)
        {
            _overlayDialogConfig.DisplayType = displayType;
            _overlayDialogConfig.TargetTransform = FloatingTextAnchor;
            _overlayDialogConfig.ShowBackground = true;
            _overlayDialogConfig.TextToDisplay = textId;
            _overlayDialogConfig.TextArgs = args;
            _overlayDialogManager.ShowOverlayDialog(_overlayDialogConfig);
        }

        protected ScreenType GetCurrentScreenType()
        {
            return _screensManager.GetCurrentScreenType();
        }

        private void TimerExpiredHandler()
        {
            if (_isShown)
            {
                OnShowInternal();
            }
        }

        protected virtual void CurrenciesScheduledToVisualizeHandler(IReadOnlyCollection<(string currencyUid, int deltaValue)> currencies)
        {
            _interrupted = false;
        }

        private void AnimateDelta(CurrencyDictionary currencyDictionary, float timeTillFirstLand)
        {
            var currencyUid = CurrencyUid;
            if (currencyUid.IsNullOrEmpty())
                return;

            if (!currencyDictionary.ContainsKey(currencyUid))
                return;

            var delta = (int)currencyDictionary[currencyUid];
            if (delta <= 0)
                return;

            if (!_isShown)
            {
                TryToShow();
                if (!_isShown)
                    return;
            }

            _currencyAnimationInProcess = true;
            if (!HudBlockUid.IsNullOrEmpty())
            {
                GenericHudManager.BlockBy(HudBlockUid, true);
            }

            CancelableTweeners.Add(Rx.Invoke(timeTillFirstLand, _ =>
            {
                if (!_isShown || _interrupted)
                {
                    EndHighlight();
                    return;
                }

                StartHighlight(delta);
            }));
        }

        protected virtual void StartHighlight(int deltaValue)
        {
            if (deltaValue > 0)
            {
                _deltaText.text = string.Format(_deltaMaskStringFormatMask, deltaValue);
                _fxAnimator.SetTrigger(Highlight);
            }

            CancelableTweeners.Add(Rx.Invoke(_interrupted ? 0 : MaxHighlightTime, _ => EndHighlight()));
        }

        protected virtual void EndHighlight()
        {
            _currencyAnimationInProcess = false;
            CancelableTweeners.Clear();
            RefreshVisibility();

            if (!HudBlockUid.IsNullOrEmpty())
            {
                GenericHudManager.BlockBy(HudBlockUid, false);
            }
        }

        private void LevelRewardInterruptionEventHandler(LevelRewardInterruptionEvent obj)
        {
            _interrupted = true;
            KillTweeners();
        }

        private void KillTweeners()
        {
            if (CancelableTweeners.Count == 0)
                return;

            _safeCancelableTweeners.AddRange(CancelableTweeners);
            CancelableTweeners.Clear();

            foreach (var tweener in _safeCancelableTweeners)
            {
                tweener?.Kill(true);
            }

            _safeCancelableTweeners.Clear();
        }

        private void RefreshNotifier()
        {
            if (_notifierHolder == null)
                return;

            var notifier = Notifier;
            if (notifier != null)
            {
                _notifierWidget.Init(notifier);
                _notifierHolder.SetActive(true);
            }
            else
            {
                _notifierHolder.SetActive(false);
            }
        }

        private void RefreshTimer()
        {
            if (_clockCountdown == null)
                return;

            // init can retrigger expiration, which leads invokation loop
            _clockCountdown.TimerExpired -= TimerExpiredHandler;

            var shouldShowTimer = ShouldShowTimer;
            if (shouldShowTimer)
            {
                _clockCountdown.Init(_localizationManager, GetRemainingTime);
                _clockCountdown.TimerExpired += TimerExpiredHandler;
            }

            ShowTimer(shouldShowTimer);
        }

        public override string ToString()
        {
            return $"{gameObject.name}, {SlotUid}";
        }
    }
}