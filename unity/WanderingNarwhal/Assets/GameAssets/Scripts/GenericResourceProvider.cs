using System.Collections.Generic;
using BBB;
using UnityEngine.U2D;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Promotions.Banners;
using GameAssets.Scripts.Utils;
using GameAssets.Scripts.Wallet.Visualizing;
using UnityEngine;
using UnityEngine.Audio;

public class GenericResourceProvider : ResourceProviderBase
{
    public const string FallbackOfflineAvatar = "avatar_fennec";

    public const string DefaultAutoAvatar = "avatar_fennec";
    
    public const string FacebookAvatar = "avatar_facebook";

    public static bool IsDefaultAvatar(string avatar)
    {
        return avatar is GameConstants.OldAutoAvatar or DefaultAutoAvatar || DefaultAvatars.Contains(avatar);
    }
    
    public static readonly List<string> DefaultAvatars = new()
    {
        "avatar_fennec",
        "avatar_captain",
        "avatar_coach",
        "avatar_didi",
        "avatar_elie",
        "avatar_fauna",
        "avatar_haute",
        "avatar_henry",
        "avatar_lils",
        "avatar_pepper",
        "avatar_pepsi",
        "avatar_toad",
    };
    
    public static readonly List<string> DefaultAvatarsFrames = new()
    {
        "Default_AvatarFrame_Gold",
        "Default_AvatarFrame_Blue",
        "Default_AvatarFrame_Green",
        "Default_AvatarFrame_Pink",
        "Default_AvatarFrame_Red",
    };

    private static readonly string[] ResourcesToReleaseNames = {
        GenericResKeys.GrayOutAnimMaterial,
        GenericResKeys.SkeletonGraphicDefault,
        GenericResKeys.MapContainerTemplate,
        GenericResKeys.TapHand,
        GenericResKeys.TutorialPrefab,
        GenericResKeys.HudPrefab,
        GenericResKeys.WalletHud,
        GenericResKeys.GenericMusicContext,
        GenericResKeys.GenericSoundsContext,
        GenericResKeys.GenericVoiceoverContext,
        GenericResKeys.OverlayEffectSettings,
        GenericResKeys.BannersConfig,
        GenericResKeys.SpeechBubblePrefab,
        GenericResKeys.FloatingTextPrefab,
        GenericResKeys.FloatingTextEpisodePrefab
    };
    
    public GenericResourceProvider() : base(true)
    {
    }

    public override void CacheResources(IResourceCache resourceCache)
    {
        PreloadedResourcePack.DisposeResources();
        PreloadedResourcePack.AddResourceToPreload<GameObject>(this, GenericResKeys.MapContainerTemplate, resourceCache);
        PreloadedResourcePack.AddResourceToPreload<GameObject>(this, GenericResKeys.TapHand, resourceCache);
        PreloadedResourcePack.AddResourceToPreload<GameObject>(this, GenericResKeys.TutorialPrefab, resourceCache);
        PreloadedResourcePack.AddResourceToPreload<GameObject>(this, GenericResKeys.HudPrefab, resourceCache);
        PreloadedResourcePack.AddResourceToPreload<GameObject>(this, GenericResKeys.WalletHud, resourceCache);
        PreloadedResourcePack.AddResourceToPreload<AudioContextSettings>(this, GenericResKeys.GenericMusicContext, resourceCache);
        PreloadedResourcePack.AddResourceToPreload<AudioContextSettings>(this, GenericResKeys.GenericSoundsContext, resourceCache);
        PreloadedResourcePack.AddResourceToPreload<AudioContextSettings>(this, GenericResKeys.GenericVoiceoverContext, resourceCache);
        PreloadedResourcePack.AddResourceToPreload<OverlayEffectSettings>(this, GenericResKeys.OverlayEffectSettings, resourceCache);
        PreloadedResourcePack.AddResourceToPreload<BannersConfig>(this, GenericResKeys.BannersConfig, resourceCache);
        PreloadedResourcePack.AddResourceToPreload<GameObject>(this, GenericResKeys.SpeechBubblePrefab, resourceCache);
        PreloadedResourcePack.AddResourceToPreload<GameObject>(this, GenericResKeys.FloatingTextPrefab, resourceCache);
        PreloadedResourcePack.AddResourceToPreload<GameObject>(this, GenericResKeys.FloatingTextEpisodePrefab, resourceCache);

        foreach (var mixerPath in GenericResKeys.MixerPaths)
        {
            PreloadedResourcePack.AddResourceToPreload<AudioMixer>(this, mixerPath, resourceCache);
        }
    }
    
    protected override void OnReleaseByContext(IContext context)
    {
        ReleaseCached(ResourcesToReleaseNames);
        
        base.OnReleaseByContext(context);
    }
}
