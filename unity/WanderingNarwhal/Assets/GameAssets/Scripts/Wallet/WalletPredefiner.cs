using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using PBGame;
using BBB.Core.Wallet;
using UnityEngine;

namespace BBB.Wallet
{
    public class WalletCurrencies
    {
        public const string RegularCurrency = "regular";
        public const string PremiumCurrency = "premium";
        public const string VipCurrency = "vip";
        public const string LifeCurrency = "life";

        public const string RegularCurrencyLocKey = "RESOURCE_REGULAR_NAME";
        public const string PremiumCurrencyLocKey = "RESOURCE_PREMIUM_NAME";

        public static readonly List<string> CurrenciesUids = new()
        {
            RegularCurrency,
            PremiumCurrency,
            LifeCurrency,
            VipCurrency,
        };

        public static bool IsCurrency(string currencyUid)
        {
            return CurrenciesUids.Contains(currencyUid);
        }
    }

    public class WalletResources
    {
        public const string ArtCurrency = "art";
        public const string AdventureCurrency = "adventure";
        public const string CultureCurrency = "culture";
        public const string GreenCurrency = "green";
        public const string TravelCurrency = "travel";
        public const string WorkerCurrency = "worker";

        public const string HardCurrency = "hard";

        public static readonly List<string> ResourcesUids = new()
        {
            ArtCurrency,
            AdventureCurrency,
            CultureCurrency,
            GreenCurrency,
            TravelCurrency,
            WorkerCurrency
        };

        public static bool IsStarCurrency(string currencyUid)
        {
            return currencyUid is HardCurrency;
        }
    }

    public class InventoryBoosters
    {
        public const string ShovelBooster = "shovel";
        public const string LineCrushBooster = "linecrush";
        public const string BombBooster = "bomb";
        public const string LightningStrikeBooster = "lightningstrike";
        public const string PropellerBooster = "propeller";
        public const string PropellerBoosterButler = "propeller_butler";
        public const string BombBoosterButler = "bomb_butler";
        public const string ColorBombBoosterButler = "colorBomb_butler";
        public const string LineBreakerBoosterButler = "lineBreaker_butler";
        public const string ExtraMovesButler = "extra_move_butler";
        public const string VerticalBooster = "vertical";
        public const string HorizontalBooster = "horizontal";
        
        public static readonly List<string> AllBoosters = new ()
        {
            ShovelBooster,
            LineCrushBooster,
            BombBooster,
            LightningStrikeBooster,
            PropellerBooster,
            PropellerBoosterButler,
            BombBoosterButler,
            ColorBombBoosterButler,
            LineBreakerBoosterButler,
            ExtraMovesButler,
            VerticalBooster,
            HorizontalBooster,
        };
    }

    public class InventoryItems
    {
        public static readonly List<string> BoosterUids = new()
        {
            InventoryBoosters.ShovelBooster,
            InventoryBoosters.LineCrushBooster,
            InventoryBoosters.BombBooster,
            InventoryBoosters.LightningStrikeBooster,
            InventoryBoosters.VerticalBooster,
            InventoryBoosters.HorizontalBooster,
            InfLife,
            InfRocket,
            InfBomb,
            InfBolt,
            ExtraMoves,
        };

        public static readonly List<string> OrderOfPhysicalRewards = new() { Shirt, Mug, Portrait, Hat };

        public const string InfLife = "life_inf";
        public const string InfRocket = "linecrush_inf";
        public const string InfBomb = "bomb_inf";
        public const string InfBolt = "lightningstrike_inf";
        public const string PlusOne = "plusone";
        public const string ExtraMoves = "extra_moves_5";
        public const string WeeklyTournament = "weekly_tournament";
        public const string ExtraMoveBooster = "extra_move";

        private const string RaceEventTx = "race_event_{0}";
        private const string TeamCoopEventTx = "team_coop_event_{0}";

        // TODO: get rid of that by refactoring or implement on config side
        public static string GetRaceEventStageCurrency(string stageUid)
        {
            return string.Format(RaceEventTx, GetKnownStageUid(stageUid));
        }

        public static string GetTeamEventCurrency(string eventUid)
        {
            return string.Format(TeamCoopEventTx, eventUid);
        }

        private static string GetKnownStageUid(string stageUid)
        {
            var knownStages = new List<string>()
            {
                "worldrace_karting",
                "discorush_colorbomb",
                "foodcarnival_stage1",
                "foodcarnival_stage2",
                "foodcarnival_stage3",
                "foodcarnival_stage4",
            };

            if (knownStages.Contains(stageUid))
                return stageUid;

            foreach (var knownStageUid in knownStages)
            {
                if (stageUid.Contains(knownStageUid))
                    return knownStageUid;
            }

            return knownStages[0];
        }

        public const string Shirt = "tshirt";
        public const string Mug = "mug";
        public const string Portrait = "portrait";
        public const string SpecialReward = "special_reward";
        public const string Hat = "hat";

        public const string GameEventScore = "game_event_score";
        public const string CollectionGameEventScore = "collection_game_event_score";
        public const string CompetitionGameEventScore = "competition_game_event_score";
        public const string SideMapEventScore = "side_map_event_score";
        public const string EndOfContentEventScore = "end_of_content_event_score";
        public const string SweepstakesEventScore = "sweepstakes_event_score";

        public const string ChallengeTriviaScore = "challenge_trivia_score";

        public static bool IsGameEventScore(string curUid)
        {
            return curUid is GameEventScore or CompetitionGameEventScore or CollectionGameEventScore or SideMapEventScore
                or EndOfContentEventScore or SweepstakesEventScore;
        }

        public static bool IsUpbGameEventScore(string curUid)
        {
            return curUid is GameEventScore or CollectionGameEventScore or SweepstakesEventScore;
        }

        public static GameEventGameplayType GetGameplayTypeByScoreUid(string scoreUid)
        {
            return scoreUid switch
            {
                CompetitionGameEventScore => GameEventGameplayType.Competition,
                SideMapEventScore => GameEventGameplayType.SideMap,
                EndOfContentEventScore => GameEventGameplayType.EndOfContent,
                CollectionGameEventScore => GameEventGameplayType.Collection,
                SweepstakesEventScore => GameEventGameplayType.Sweepstakes,
                _ => GameEventGameplayType.None
            };
        }

        public static string GetGameEventScoreUid(GameEventGameplayType gameplayType)
        {
            return gameplayType switch
            {
                GameEventGameplayType.Competition => CompetitionGameEventScore,
                GameEventGameplayType.SideMap => SideMapEventScore,
                GameEventGameplayType.EndOfContent => EndOfContentEventScore,
                GameEventGameplayType.Collection => CollectionGameEventScore,
                GameEventGameplayType.Sweepstakes => SweepstakesEventScore,
                _ => GameEventScore
            };
        }

        public static bool IsSpecialGameEventScore(string scoreUid)
        {
            return GetGameplayTypeByScoreUid(scoreUid) != GameEventGameplayType.None;
        }

        public static bool IsSpecialEventGameplayType(GameEventGameplayType gamePlayType)
        {
            return GetGameEventScoreUid(gamePlayType) != GameEventScore;
        }

        public static readonly List<string> InfItems = new()
        {
            InfLife, InfRocket, InfBomb, InfBolt
        };

        public static bool IsInfinite(string uid)
        {
            return InfItems.Contains(uid);
        }
    }

    public static class RewardsAmountStringFormatDefinitions
    {
        /// <summary>
        /// Optional overrides for displayed amount of currency or inventory item, given in reward.
        /// </suwmmary>
        /// <remarks>
        /// When player receives reward, for most currencies we show currency icon and amount number,
        /// but for some currencies we need to show amount in special string format, defined by localization config.
        /// Example: infinite lives duration minutes count, which should be displayed as amount of minutes.
        /// </remarks>
        private static readonly Dictionary<string, string> CurrenciesAmountSpecialStrings = new()
        {
            { InventoryItems.InfLife, "REWARD_AMOUNT_FORMAT_INF_LIVES" },
            { InventoryItems.Shirt, "TSHIRT_NAME" },
            { InventoryItems.Mug, "MUG_NAME" },
            { InventoryItems.Portrait, "PORTRAIT_NAME" },
            { InventoryItems.Hat, "HAT_NAME" },
        };

        private static readonly Dictionary<string, string> ShortCurrenciesAmountSpecialStrings = new()
        {
            { InventoryItems.InfLife, "REWARD_AMOUNT_FORMAT_INF_LIVES_SHORT" },
            { InventoryItems.Shirt, "TSHIRT_NAME" },
            { InventoryItems.Mug, "MUG_NAME" },
            { InventoryItems.Portrait, "PORTRAIT_NAME" },
            { InventoryItems.Hat, "HAT_NAME" },
        };

        // This category exists for currencies which has number text on the icon itself
        private static readonly HashSet<string> CurrenciesToSkipText = new()
        {
            WalletCurrencies.LifeCurrency,
            InventoryItems.ExtraMoves,
        };

        private const string InfLivesMins = "INF_LIVES_TIME_M";
        private const string InfLivesHours = "INF_LIVES_TIME_H";
        private const string InfLivesHoursAndMins = "INF_LIVES_TIME_HM";
        private const string UnitPrefixCurrencyFormat = "{0} K";

        public static string GetUnitPrefixFormattedCurrency(string currencyUid, long currencyValue, ILocalizationManager localization)
        {
            if (WalletCurrencies.IsCurrency(currencyUid))
            {
                if (currencyValue >= 100_000)
                {
                    var thousands = Mathf.FloorToInt(currencyValue * 0.001f);
                    return string.Format(UnitPrefixCurrencyFormat, thousands.ToString(CultureInfo.InvariantCulture));
                }

                return currencyValue.ToString();
            }

            return LocalizeCurrencyCount(currencyUid, currencyValue, localization, true);
        }

        /// <summary>
        /// Convert currency count string if exist currency count localization key.
        /// </summary>
        /// <param name="currencyUid">Currency or inventory item uid.</param>
        /// <param name="count">Count string.</param>
        /// <param name="localization">localizer.</param>
        /// <returns>Localized count text.</returns>
        /// <remarks>
        /// For example, 'life_inf = 15' item count text will be localized as '15 min' value instead of regular '15' value.
        /// City ticket inventory item will show city name instead of count.
        /// </remarks>
        public static string LocalizeCurrencyCount(string currencyUid, long currencyValue, ILocalizationManager localization, bool shortVersion = false, bool addXSignIfNeeded = false)
        {
            if (CurrenciesToSkipText.Contains(currencyUid))
                return string.Empty;

            var locKey = string.Empty;

            var currencyValueText = currencyValue.ToString();
            var isInfinite = InventoryItems.IsInfinite(currencyUid);

            if (localization != null && isInfinite)
                currencyValueText = PreProcessInfItem(currencyValueText, localization);

            locKey = shortVersion switch
            {
                false when CurrenciesAmountSpecialStrings.TryGetValue(currencyUid, out var amountSpecialString) =>
                    amountSpecialString,
                true when ShortCurrenciesAmountSpecialStrings.TryGetValue(currencyUid, out var shortAmountSpecialString) =>
                    shortAmountSpecialString,
                _ => locKey
            };

            if (!locKey.IsNullOrEmpty() && localization != null)
            {
                var localized = localization.getLocalizedText(locKey);
                if (localized.Contains("{0}"))
                {
                    return string.Format(localized, currencyValueText);
                }

                if (localized != locKey)
                {
                    return localized;
                }
            }
            else
            {
                if (addXSignIfNeeded && !isInfinite && !WalletCurrencies.IsCurrency(currencyUid))
                    return $"x{currencyValueText}";
            }

            return currencyValueText;
        }

        private static string PreProcessInfItem(string countString, ILocalizationManager localization)
        {
            if (!int.TryParse(countString, out var count))
                return countString;

            const int hour = 60;
            if (count >= hour)
            {
                return count % hour == 0 ? localization.getLocalizedTextWithArgs(InfLivesHours, count / hour) : localization.getLocalizedTextWithArgs(InfLivesHoursAndMins, count / hour, count % hour);
            }

            return localization.getLocalizedTextWithArgs(InfLivesMins, count);
        }
    }

    public static class CurrenciesPriorityProvider
    {
        private static readonly List<string> CurrenciesByPriority = new()
        {
            WalletCurrencies.RegularCurrency,
            WalletCurrencies.PremiumCurrency,
            WalletCurrencies.LifeCurrency,
            WalletCurrencies.VipCurrency,

            WalletResources.ArtCurrency,
            WalletResources.AdventureCurrency,
            WalletResources.CultureCurrency,
            WalletResources.GreenCurrency,
            WalletResources.TravelCurrency,
            WalletResources.WorkerCurrency,
        };

        public static int GetPriorityOfCurrency(string currencyUid)
        {
            return CurrenciesByPriority.Contains(currencyUid) ? CurrenciesByPriority.IndexOf(currencyUid) : int.MaxValue;
        }
    }

    public class WalletPredefiner : IWalletPredefiner
    {
        private readonly PBWallet _wallet;
        private Dictionary<WalletType, IWallet> _walletByType;
        private Dictionary<string, WalletType> _walletTypeByCurrency;
        public IReadOnlyDictionary<WalletType, IWallet> WalletByType => _walletByType;
        public IReadOnlyDictionary<string, WalletType> WalletTypeByCurrency => _walletTypeByCurrency;

        public WalletPredefiner(PBWallet wallet)
        {
            _wallet = wallet;
            _wallet.WalletCurrency ??= new Dictionary<string, PBWalletCurrency>();
            _wallet.WalletResource ??= new Dictionary<string, PBWalletResource>();

            InitCurrenciesAndResources();
            InitWallets();
        }

        private void InitCurrenciesAndResources()
        {
            _walletTypeByCurrency = new Dictionary<string, WalletType>();

            foreach (var currencyUid in WalletCurrencies.CurrenciesUids)
            {
                _walletTypeByCurrency.Add(currencyUid, WalletType.CurrencyWallet);
            }

            foreach (var resourceUid in WalletResources.ResourcesUids)
            {
                _walletTypeByCurrency.Add(resourceUid, WalletType.ResourcesWallet);
            }
        }

        private void InitWallets()
        {
            _walletByType = new Dictionary<WalletType, IWallet>
            {
                { WalletType.CurrencyWallet, new CurrencyWallet(_wallet.WalletCurrency) },
                { WalletType.ResourcesWallet, new ResourceWallet(_wallet.WalletResource) }
            };

            foreach (var currency in WalletTypeByCurrency.Keys)
            {
                switch (WalletTypeByCurrency[currency])
                {
                    case WalletType.CurrencyWallet:
                        if (!_wallet.WalletCurrency.ContainsKey(currency))
                            _wallet.WalletCurrency.Add(currency, new PBWalletCurrency());
                        break;
                    case WalletType.ResourcesWallet:
                        if (!_wallet.WalletResource.ContainsKey(currency))
                            _wallet.WalletResource.Add(currency, new PBWalletResource());
                        break;
                }
            }
        }
    }
}