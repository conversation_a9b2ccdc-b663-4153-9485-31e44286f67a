using System;
using System.ComponentModel;
using BebopBee.Social;
using Newtonsoft.Json;
using Realms;

namespace BBB.BrainCloud
{
    [Serializable]
    public class BCSummaryFriendData : EmbeddedObject
    {
        public string Uid { get; set; }
        public string LastUnlockedLocationId { get; set; }
        public string HighestPassedLevelId { get; set; }
        public int Trophies { get; set; }
        [DefaultValue(ProfileUtils.DefaultCountry)]            
        [JsonProperty(DefaultValueHandling = DefaultValueHandling.Populate)]
        public string Country { get; set; }
        public int? LastActivityTimestamp { get; set; }
        public string ProvidedEmail { get; set; }
        public int? HelpCount { get; set; }
        [Obsolete]
        public string LastLevelPassedId { get; set; }

        public double? InstallDate { get; set; }
        public string TeamName { get; set; }
        public string TeamIcon { get; set; }
        public int? FirstTryWins { get; set; }
        public int? HelpsReceived { get; set; }
        public int? ScenesCompleted { get; set; }
        public int? SetsCompleted { get; set; }
        public int? LeaguesWon { get; set; }
        public bool? IsBot { get; set; }
        public string AvatarFrame { get; set; }
        public string BadgeUid { get; set; }

        public override bool Equals(object obj)
        {
            return ReferenceEquals(this, obj) || (obj is BCSummaryFriendData friendData && 
                   Uid == friendData.Uid && 
                   LastUnlockedLocationId == friendData.LastUnlockedLocationId && 
                   HighestPassedLevelId == friendData.HighestPassedLevelId &&
                   Trophies == friendData.Trophies &&
                   Country == friendData.Country &&
                   LastActivityTimestamp == friendData.LastActivityTimestamp &&
                   HelpCount == friendData.HelpCount &&
                   InstallDate == friendData.InstallDate &&
                   TeamName == friendData.TeamName &&
                   TeamIcon == friendData.TeamIcon &&
                   FirstTryWins == friendData.FirstTryWins &&
                   HelpsReceived == friendData.HelpsReceived &&
                   ScenesCompleted == friendData.ScenesCompleted &&
                   SetsCompleted == friendData.SetsCompleted &&
                   LeaguesWon == friendData.LeaguesWon &&
                   IsBot == friendData.IsBot &&
                   AvatarFrame == friendData.AvatarFrame &&
                   BadgeUid == friendData.BadgeUid);
        }
        
        public override int GetHashCode()
        {
            var hashCode = new HashCode();
            hashCode.Add(Uid);
            hashCode.Add(LastUnlockedLocationId);
            hashCode.Add(HighestPassedLevelId);
            hashCode.Add(Trophies);
            hashCode.Add(Country);
            hashCode.Add(LastActivityTimestamp);
            hashCode.Add(HelpCount);
            hashCode.Add(InstallDate);
            hashCode.Add(TeamName);
            hashCode.Add(TeamIcon);
            hashCode.Add(FirstTryWins);
            hashCode.Add(HelpsReceived);
            hashCode.Add(ScenesCompleted);
            hashCode.Add(SetsCompleted);
            hashCode.Add(LeaguesWon);
            hashCode.Add(IsBot);
            hashCode.Add(AvatarFrame);
            hashCode.Add(BadgeUid);
            return hashCode.ToHashCode();
        }
    }
}