%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1000010118178796
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 224000012899738160}
  - component: {fileID: 114207002494005054}
  - component: {fileID: 1023396320158798028}
  - component: {fileID: 2542825896162392316}
  m_Layer: 5
  m_Name: IvyOverlay
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &224000012899738160
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1000010118178796}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5279332708054229017}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -319.67, y: -568.58636}
  m_SizeDelta: {x: 68, y: 68}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &114207002494005054
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1000010118178796}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: db47456236e54dd98d70f030d714d18f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1023396320158798028
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1000010118178796}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f2eab83599c44e99ac277b8ba8c73c1f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _initialized: 1
  _components:
  - {fileID: 6337194008915574155}
  - {fileID: 114207002494005054}
  - {fileID: 1023396320158798028}
  - {fileID: 2542825896162392316}
  SpriteRenderer: {fileID: 0}
  OverlayType: 0
  _feedbackSoundUid: 
  _sk: {fileID: 6337194008915574155}
  _revealDelay: 1.4
  _revealAnimName: Appear
  _idleAnimName: Idle
  _feedbackToMachAnimName: Feedback
  _feedbackToFallAnimName: Feedback_falling_through
--- !u!223 &2542825896162392316
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1000010118178796}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 1
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 0
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!1 &5137184523315557371
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5279332708054229017}
  - component: {fileID: 3233820636188871371}
  - component: {fileID: 6337194008915574155}
  m_Layer: 5
  m_Name: IvySpine
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5279332708054229017
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5137184523315557371}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1.1, y: 1.1, z: 1.1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 224000012899738160}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -0.000030517578, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &3233820636188871371
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5137184523315557371}
  m_CullTransparentMesh: 0
--- !u!114 &6337194008915574155
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5137184523315557371}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d85b887af7e6c3f45a2e2d2920d641bc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: 2f7f56498fa93a24cb137918c360fb41, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  skeletonDataAsset: {fileID: 11400000, guid: f15b59b3b874acf4fa85b4807f149024, type: 2}
  additiveMaterial: {fileID: 0}
  multiplyMaterial: {fileID: 0}
  screenMaterial: {fileID: 0}
  m_SkeletonColor: {r: 1, g: 1, b: 1, a: 1}
  initialSkinName: default
  initialFlipX: 0
  initialFlipY: 0
  startingAnimation: Idle
  startingLoop: 1
  timeScale: 1
  freeze: 0
  layoutScaleMode: 4
  referenceSize: {x: 1.0627899, y: 1}
  pivotOffset: {x: 0, y: 0}
  referenceScale: 0.004117871
  rectTransformSize: {x: 1, y: 1}
  editReferenceRect: 0
  updateWhenInvisible: 3
  allowMultipleCanvasRenderers: 0
  canvasRenderers: []
  separatorSlotNames: []
  enableSeparatorSlots: 0
  separatorParts: []
  updateSeparatorPartLocation: 1
  updateSeparatorPartScale: 0
  disableMeshAssignmentOnOverride: 1
  physicsPositionInheritanceFactor: {x: 1, y: 1}
  physicsRotationInheritanceFactor: 1
  physicsMovementRelativeTo: {fileID: 0}
  meshGenerator:
    settings:
      useClipping: 1
      zSpacing: 0
      tintBlack: 0
      canvasGroupCompatible: 0
      pmaVertexColors: 1
      addNormals: 0
      calculateTangents: 0
      immutableTriangles: 0
  updateTiming: 1
  unscaledTime: 0
