using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Core.Configs;
using FBConfig;
using PBConfig;
using UnityEditor;
using UnityEngine;

using BundleIndexConfig = FBConfig.BundleIndexConfig;
using BundleIndexConfigDict = FBConfig.BundleIndexConfigDict;
using BundleInfoConfig = FBConfig.BundleInfoConfig;
using BundleInfoConfigDict = FBConfig.BundleInfoConfigDict;
using DefaultNamesConfig = FBConfig.DefaultNamesConfig;
using DefaultNamesConfigDict = FBConfig.DefaultNamesConfigDict;
using GachaConfig = FBConfig.GachaConfig;
using GachaConfigDict = FBConfig.GachaConfigDict;
using GameUpdateConfig = FBConfig.GameUpdateConfig;
using GameUpdateConfigDict = FBConfig.GameUpdateConfigDict;
using IAPStoreMarketItemConfig = FBConfig.IAPStoreMarketItemConfig;
using IAPStoreMarketItemConfigDict = FBConfig.IAPStoreMarketItemConfigDict;
using LocalPushNotificationsTimingConfig = FBConfig.LocalPushNotificationsTimingConfig;
using LocalPushNotificationsTimingConfigDict = FBConfig.LocalPushNotificationsTimingConfigDict;
using LockItemConfig = FBConfig.LockItemConfig;
using LockItemConfigDict = FBConfig.LockItemConfigDict;
using QuestConfig = FBConfig.QuestConfig;
using QuestConfigDict = FBConfig.QuestConfigDict;
using ScreenConfig = FBConfig.ScreenConfig;
using ScreenConfigDict = FBConfig.ScreenConfigDict;
using SpriteAtlasIndexConfig = FBConfig.SpriteAtlasIndexConfig;
using SpriteAtlasIndexConfigDict = FBConfig.SpriteAtlasIndexConfigDict;
using SystemConfig = FBConfig.SystemConfig;
using SystemConfigDict = FBConfig.SystemConfigDict;
using ThemeConfig = FBConfig.ThemeConfig;
using ThemeConfigDict = FBConfig.ThemeConfigDict;
using ThemeMetaConfig = FBConfig.ThemeMetaConfig;
using ThemeMetaConfigDict = FBConfig.ThemeMetaConfigDict;
using TransitionTipsConfig = FBConfig.TransitionTipsConfig;
using TransitionTipsConfigDict = FBConfig.TransitionTipsConfigDict;
using TutorialConfig = FBConfig.TutorialConfig;
using TutorialConfigDict = FBConfig.TutorialConfigDict;
using UnifiedPromotionConfig = FBConfig.UnifiedPromotionConfig;
using UnifiedPromotionConfigDict = FBConfig.UnifiedPromotionConfigDict;
using VIPProductsMetaConfg = FBConfig.VIPProductsMetaConfg;
using VIPProductsMetaConfgDict = FBConfig.VIPProductsMetaConfgDict;
using AdsConfigDict = FBConfig.AdsConfigDict;
using AdsConfig = FBConfig.AdsConfig;
using IAPCategoryConfig = FBConfig.IAPCategoryConfig;
using IAPCategoryConfigDict = FBConfig.IAPCategoryConfigDict;
using IAPStoreCategoryConfigDict = FBConfig.IAPStoreCategoryConfigDict;
using IAPStoreCategoryConfig = FBConfig.IAPStoreCategoryConfig;
using IAPStoreVirtualItemPackConfigDict = FBConfig.IAPStoreVirtualItemPackConfigDict;
using IAPStoreVirtualItemPackConfig = FBConfig.IAPStoreVirtualItemPackConfig;
using OfferConfigDict = FBConfig.OfferConfigDict;
using OfferConfig = FBConfig.OfferConfig;
using BoosterConfigDict = FBConfig.BoosterConfigDict;
using BoosterConfig = FBConfig.BoosterConfig;
using SuperBoostConfigDict = FBConfig.SuperBoostConfigDict;
using SuperBoostConfig = FBConfig.SuperBoostConfig;
using EndlessTreasureConfigDict = FBConfig.EndlessTreasureConfigDict;
using EndlessTreasureConfig = FBConfig.EndlessTreasureConfig;
using RaceStageConfigDict = FBConfig.RaceStageConfigDict;
using RaceStageConfig = FBConfig.RaceStageConfig;
using TeamEventConfigDict = FBConfig.TeamEventConfigDict;
using TeamEventConfig = FBConfig.TeamEventConfig;
using CompetitionGameEventConfigDict = FBConfig.CompetitionGameEventConfigDict;
using CompetitionGameEventConfig = FBConfig.CompetitionGameEventConfig;
using LeadLevelConfigDict = FBConfig.LeadLevelConfigDict;
using LeadLevelConfig = FBConfig.LeadLevelConfig;
using Match3AwardsConfigDict = FBConfig.Match3AwardsConfigDict;
using Match3AwardsConfig = FBConfig.Match3AwardsConfig;
using Match3SpecialVoiceoversConfigDict = FBConfig.Match3SpecialVoiceoversConfigDict;
using Match3SpecialVoiceoversConfig = FBConfig.Match3SpecialVoiceoversConfig;
using LivesConfigDict = FBConfig.LivesConfigDict;
using LivesConfig = FBConfig.LivesConfig;
using HintSystemConfigDict = FBConfig.HintSystemConfigDict;
using HintSystemConfig = FBConfig.HintSystemConfig;
using SuperBoostProgressConfigDict = FBConfig.SuperBoostProgressConfigDict;
using SuperBoostProgressConfig = FBConfig.SuperBoostProgressConfig;
using VIPProductsConfgDict = FBConfig.VIPProductsConfgDict;
using VIPProductsConfg = FBConfig.VIPProductsConfg;
using GoalScoreConfigDict = FBConfig.GoalScoreConfigDict;
using GoalScoreConfig = FBConfig.GoalScoreConfig;
using FakeUsersConfigDict = FBConfig.FakeUsersConfigDict;
using FakeUsersConfig = FBConfig.FakeUsersConfig;
using LocalNotificationsConfigDict = FBConfig.LocalNotificationsConfigDict;
using LocalNotificationsConfig = FBConfig.LocalNotificationsConfig;
using LevelNarrativeConfigDict = FBConfig.LevelNarrativeConfigDict;
using LevelNarrativeConfig = FBConfig.LevelNarrativeConfig;
using CarrotsConfigDict = FBConfig.CarrotsConfigDict;
using CarrotsConfig = FBConfig.CarrotsConfig;
using SdbConfigDict = FBConfig.SdbConfigDict;
using SdbConfig = FBConfig.SdbConfig;
using SweepstakesDailyLoginConfigDict = FBConfig.SweepstakesDailyLoginConfigDict;
using SweepstakesDailyLoginConfig = FBConfig.SweepstakesDailyLoginConfig;
using SweepStakesGameEventConfigDict = FBConfig.SweepStakesGameEventConfigDict;
using SweepStakesGameEventConfig = FBConfig.SweepStakesGameEventConfig;
using SweepstakesVideoConfigDict = FBConfig.SweepstakesVideoConfigDict;
using SweepstakesVideoConfig = FBConfig.SweepstakesVideoConfig;
using GiantPinataOutcomeConfigDict = FBConfig.GiantPinataOutcomeConfigDict;
using GiantPinataOutcomeConfig = FBConfig.GiantPinataOutcomeConfig;
using ProfileCustomizationConfig = FBConfig.ProfileCustomizationConfig;
using ProfileCustomizationConfigDict = FBConfig.ProfileCustomizationConfigDict;

public static class ConfigResolverRegistryGenerator
{
    private const string OutputFolder = "Assets/Scripts/Generated";
    private const string OutputFile   = "AppController.Config.cs";

    [MenuItem("BebopBee/Configs/Generate Resolver Registry")]
    public static void Generate()
    {
        var importedNamespaces = new HashSet<string>
        {
            "System.Collections.Generic",
            "Core.Configs",
            "FBConfig",
            "PBConfig"
        };
        
        var sb = new StringBuilder();
        foreach (var importedName in importedNamespaces)
        {
            sb.Append("using ").Append(importedName).Append(";").AppendLine();
        }
        
        sb.AppendLine("using BundleIndexConfig = FBConfig.BundleIndexConfig;")
            .AppendLine("using BundleIndexConfigDict = FBConfig.BundleIndexConfigDict;")
            .AppendLine("using BundleInfoConfig = FBConfig.BundleInfoConfig;")
            .AppendLine("using BundleInfoConfigDict = FBConfig.BundleInfoConfigDict;")
            .AppendLine("using DefaultNamesConfig = FBConfig.DefaultNamesConfig;")
            .AppendLine("using DefaultNamesConfigDict = FBConfig.DefaultNamesConfigDict;")
            .AppendLine("using GachaConfig = FBConfig.GachaConfig;")
            .AppendLine("using GachaConfigDict = FBConfig.GachaConfigDict;")
            .AppendLine("using GameUpdateConfig = FBConfig.GameUpdateConfig;")
            .AppendLine("using GameUpdateConfigDict = FBConfig.GameUpdateConfigDict;")
            .AppendLine("using IAPStoreMarketItemConfig = FBConfig.IAPStoreMarketItemConfig;")
            .AppendLine("using IAPStoreMarketItemConfigDict = FBConfig.IAPStoreMarketItemConfigDict;")
            .AppendLine("using LocalPushNotificationsTimingConfig = FBConfig.LocalPushNotificationsTimingConfig;")
            .AppendLine("using LocalPushNotificationsTimingConfigDict = FBConfig.LocalPushNotificationsTimingConfigDict;")
            .AppendLine("using LockItemConfig = FBConfig.LockItemConfig;")
            .AppendLine("using LockItemConfigDict = FBConfig.LockItemConfigDict;")
            .AppendLine("using QuestConfig = FBConfig.QuestConfig;")
            .AppendLine("using QuestConfigDict = FBConfig.QuestConfigDict;")
            .AppendLine("using ScreenConfig = FBConfig.ScreenConfig;")
            .AppendLine("using ScreenConfigDict = FBConfig.ScreenConfigDict;")
            .AppendLine("using SpriteAtlasIndexConfig = FBConfig.SpriteAtlasIndexConfig;")
            .AppendLine("using SpriteAtlasIndexConfigDict = FBConfig.SpriteAtlasIndexConfigDict;")
            .AppendLine("using SystemConfig = FBConfig.SystemConfig;")
            .AppendLine("using SystemConfigDict = FBConfig.SystemConfigDict;")
            .AppendLine("using ThemeConfig = FBConfig.ThemeConfig;")
            .AppendLine("using ThemeConfigDict = FBConfig.ThemeConfigDict;")
            .AppendLine("using ThemeMetaConfig = FBConfig.ThemeMetaConfig;")
            .AppendLine("using ThemeMetaConfigDict = FBConfig.ThemeMetaConfigDict;")
            .AppendLine("using TransitionTipsConfig = FBConfig.TransitionTipsConfig;")
            .AppendLine("using TransitionTipsConfigDict = FBConfig.TransitionTipsConfigDict;")
            .AppendLine("using TutorialConfig = FBConfig.TutorialConfig;")
            .AppendLine("using TutorialConfigDict = FBConfig.TutorialConfigDict;")
            .AppendLine("using UnifiedPromotionConfig = FBConfig.UnifiedPromotionConfig;")
            .AppendLine("using UnifiedPromotionConfigDict = FBConfig.UnifiedPromotionConfigDict;")
            .AppendLine("using VIPProductsMetaConfg = FBConfig.VIPProductsMetaConfg;")
            .AppendLine("using VIPProductsMetaConfgDict = FBConfig.VIPProductsMetaConfgDict;")
            .AppendLine("using AdsConfigDict = FBConfig.AdsConfigDict;")
            .AppendLine("using AdsConfig = FBConfig.AdsConfig;")
            .AppendLine("using IAPCategoryConfig = FBConfig.IAPCategoryConfig;")
            .AppendLine("using IAPCategoryConfigDict = FBConfig.IAPCategoryConfigDict;")
            .AppendLine("using IAPStoreCategoryConfigDict = FBConfig.IAPStoreCategoryConfigDict;")
            .AppendLine("using IAPStoreCategoryConfig = FBConfig.IAPStoreCategoryConfig;")
            .AppendLine("using IAPStoreVirtualItemPackConfigDict = FBConfig.IAPStoreVirtualItemPackConfigDict;")
            .AppendLine("using IAPStoreVirtualItemPackConfig = FBConfig.IAPStoreVirtualItemPackConfig;")
            .AppendLine("using OfferConfigDict = FBConfig.OfferConfigDict;")
            .AppendLine("using OfferConfig = FBConfig.OfferConfig;")
            .AppendLine("using BoosterConfigDict = FBConfig.BoosterConfigDict;")
            .AppendLine("using BoosterConfig = FBConfig.BoosterConfig;")
            .AppendLine("using SuperBoostConfigDict = FBConfig.SuperBoostConfigDict;")
            .AppendLine("using SuperBoostConfig = FBConfig.SuperBoostConfig;")
            .AppendLine("using EndlessTreasureConfigDict = FBConfig.EndlessTreasureConfigDict;")
            .AppendLine("using EndlessTreasureConfig = FBConfig.EndlessTreasureConfig;")
            .AppendLine("using RaceStageConfigDict = FBConfig.RaceStageConfigDict;")
            .AppendLine("using RaceStageConfig = FBConfig.RaceStageConfig;")
            .AppendLine("using TeamEventConfigDict = FBConfig.TeamEventConfigDict;")
            .AppendLine("using TeamEventConfig = FBConfig.TeamEventConfig;")
            .AppendLine("using CompetitionGameEventConfigDict = FBConfig.CompetitionGameEventConfigDict;")
            .AppendLine("using CompetitionGameEventConfig = FBConfig.CompetitionGameEventConfig;")
            .AppendLine("using LeadLevelConfigDict = FBConfig.LeadLevelConfigDict;")
            .AppendLine("using LeadLevelConfig = FBConfig.LeadLevelConfig;")
            .AppendLine("using Match3AwardsConfigDict = FBConfig.Match3AwardsConfigDict;")
            .AppendLine("using Match3AwardsConfig = FBConfig.Match3AwardsConfig;")
            .AppendLine("using Match3SpecialVoiceoversConfigDict = FBConfig.Match3SpecialVoiceoversConfigDict;")
            .AppendLine("using Match3SpecialVoiceoversConfig = FBConfig.Match3SpecialVoiceoversConfig;")
            .AppendLine("using LivesConfigDict = FBConfig.LivesConfigDict;")
            .AppendLine("using LivesConfig = FBConfig.LivesConfig;")
            .AppendLine("using HintSystemConfigDict = FBConfig.HintSystemConfigDict;")
            .AppendLine("using HintSystemConfig = FBConfig.HintSystemConfig;")
            .AppendLine("using SuperBoostProgressConfigDict = FBConfig.SuperBoostProgressConfigDict;")
            .AppendLine("using SuperBoostProgressConfig = FBConfig.SuperBoostProgressConfig;")
            .AppendLine("using VIPProductsConfgDict = FBConfig.VIPProductsConfgDict;")
            .AppendLine("using VIPProductsConfg = FBConfig.VIPProductsConfg;")
            .AppendLine("using GoalScoreConfigDict = FBConfig.GoalScoreConfigDict;")
            .AppendLine("using GoalScoreConfig = FBConfig.GoalScoreConfig;")
            .AppendLine("using FakeUsersConfigDict = FBConfig.FakeUsersConfigDict;")
            .AppendLine("using FakeUsersConfig = FBConfig.FakeUsersConfig;")
            .AppendLine("using LocalNotificationsConfigDict = FBConfig.LocalNotificationsConfigDict;")
            .AppendLine("using LocalNotificationsConfig = FBConfig.LocalNotificationsConfig;")
            .AppendLine("using LevelNarrativeConfigDict = FBConfig.LevelNarrativeConfigDict;")
            .AppendLine("using LevelNarrativeConfig = FBConfig.LevelNarrativeConfig;")
            .AppendLine("using CarrotsConfigDict = FBConfig.CarrotsConfigDict;")
            .AppendLine("using CarrotsConfig = FBConfig.CarrotsConfig;")
            .AppendLine("using SdbConfigDict = FBConfig.SdbConfigDict;")
            .AppendLine("using SdbConfig = FBConfig.SdbConfig;")
            .AppendLine("using SweepstakesDailyLoginConfigDict = FBConfig.SweepstakesDailyLoginConfigDict;")
            .AppendLine("using SweepstakesDailyLoginConfig = FBConfig.SweepstakesDailyLoginConfig;")
            .AppendLine("using SweepStakesGameEventConfigDict = FBConfig.SweepStakesGameEventConfigDict;")
            .AppendLine("using SweepStakesGameEventConfig = FBConfig.SweepStakesGameEventConfig;")
            .AppendLine("using SweepstakesVideoConfigDict = FBConfig.SweepstakesVideoConfigDict;")
            .AppendLine("using SweepstakesVideoConfig = FBConfig.SweepstakesVideoConfig;")
            .AppendLine("using GiantPinataOutcomeConfigDict = FBConfig.GiantPinataOutcomeConfigDict;")
            .AppendLine("using GiantPinataOutcomeConfig = FBConfig.GiantPinataOutcomeConfig;")
            .AppendLine("using ProfileCustomizationConfig = FBConfig.ProfileCustomizationConfig;")
            .AppendLine("using ProfileCustomizationConfigDict = FBConfig.ProfileCustomizationConfigDict;")
            .AppendLine()
            .AppendLine("// <auto-generated> DO NOT EDIT. Run BebopBee/Configs/Generate Resolver Registry </auto-generated>")
            .AppendLine("namespace BBB")
            .AppendLine("{")
            .AppendLine("    public partial class AppController")
            .AppendLine("    {")
            .AppendLine("        private static readonly IConfigResolver[] AllResolvers =")
            .AppendLine("        {");
        
        Emit<FlatBufferGreedyConfigResolver<BundleIndexConfigDict, BundleIndexConfig, BundleIndexConfigT>>();
        Emit<FlatBufferGreedyConfigResolver<SpriteAtlasIndexConfigDict, SpriteAtlasIndexConfig, SpriteAtlasIndexConfigT>>();
        Emit<FlatBufferGreedyConfigResolver<BundleInfoConfigDict, BundleInfoConfig, BundleInfoConfigT>>();
        // IAP 
        Emit<FlatBufferConfigResolver<IAPStoreMarketItemConfigDict, IAPStoreMarketItemConfig, IAPStoreMarketItemConfigT>>();
        Emit<FlatBufferLazyConfigResolver<IAPCategoryConfigDict, IAPCategoryConfig, IAPCategoryConfigT>>();
        
        Emit<FlatBufferLazyConfigResolver<IAPStoreCategoryConfigDict, IAPStoreCategoryConfig, IAPStoreCategoryConfigT>>();
        Emit<FlatBufferLazyConfigResolver<IAPStoreVirtualItemPackConfigDict, IAPStoreVirtualItemPackConfig, IAPStoreVirtualItemPackConfigT>>();
        Emit<FlatBufferLazyConfigResolver<OfferConfigDict, OfferConfig, OfferConfigT>>();

        // City Map Location 
        Emit<FlatBufferLazyConfigResolver<BoosterConfigDict, BoosterConfig, BoosterConfigT>>();
        
        // Gacha
        Emit<FlatBufferLazyConfigResolver<GachaConfigDict, GachaConfig, GachaConfigT>>();

        // Quest 
        Emit<FlatBufferConfigResolver<QuestConfigDict, QuestConfig, QuestConfigT>>();

        // Match-3
        Emit<FlatBufferLazyConfigResolver<ProgressionLevelConfigDict, ProgressionLevelConfig, ProgressionLevelConfigT>>();
        Emit<FlatBufferGreedyConfigResolver<SlotMachineOutcomeConfigDict, SlotMachineOutcomeConfig, SlotMachineOutcomeConfigT>>();
        Emit<FlatBufferGreedyConfigResolver<MechanicTargetingConfigDict, MechanicTargetingConfig, MechanicTargetingConfigT>>();
        
        Emit<FlatBufferLazyConfigResolver<LeadLevelConfigDict, LeadLevelConfig, LeadLevelConfigT>>();
        Emit<FlatBufferLazyConfigResolver<Match3AwardsConfigDict, Match3AwardsConfig, Match3AwardsConfigT>>();
        Emit<FlatBufferLazyConfigResolver<Match3SpecialVoiceoversConfigDict, Match3SpecialVoiceoversConfig, Match3SpecialVoiceoversConfigT>>();
        Emit<FlatBufferLazyConfigResolver<LivesConfigDict, LivesConfig, LivesConfigT>>();
        Emit<FlatBufferLazyConfigResolver<HintSystemConfigDict, HintSystemConfig, HintSystemConfigT>>();
        Emit<FlatBufferLazyConfigResolver<SuperBoostProgressConfigDict, SuperBoostProgressConfig, SuperBoostProgressConfigT>>();
        
        Emit<FlatBufferLazyConfigResolver<SuperBoostConfigDict, SuperBoostConfig, SuperBoostConfigT>>();
        Emit<FlatBufferLazyConfigResolver<GoalScoreConfigDict, GoalScoreConfig, GoalScoreConfigT>>();
        
        // Lock 
        Emit<FlatBufferConfigResolver<LockItemConfigDict, LockItemConfig, LockItemConfigT>>();

        // Screens 
        Emit<FlatBufferLazyConfigResolver<ScreenConfigDict, ScreenConfig, ScreenConfigT>>();
        Emit<FlatBufferLazyConfigResolver<TransitionTipsConfigDict, TransitionTipsConfig, TransitionTipsConfigT>>();

        // Ads
        Emit<FlatBufferLazyConfigResolver<AdsConfigDict, AdsConfig, AdsConfigT>>();
        // Social
        Emit<FlatBufferLazyConfigResolver<FakeUsersConfigDict, FakeUsersConfig, FakeUsersConfigT>>();

        // Notifications 
        Emit<FlatBufferLazyConfigResolver<LocalNotificationsConfigDict, LocalNotificationsConfig, LocalNotificationsConfigT>>();
        Emit<FlatBufferLazyConfigResolver<LocalPushNotificationsTimingConfigDict, LocalPushNotificationsTimingConfig, LocalPushNotificationsTimingConfigT>>();

        // Misc
        Emit<FlatBufferConfigResolver<GameUpdateConfigDict, GameUpdateConfig, GameUpdateConfigT>>();
        Emit<FlatBufferLazyConfigResolver<LevelNarrativeConfigDict, LevelNarrativeConfig, LevelNarrativeConfigT>>();

        Emit<ProtobufConfigResolver<NarrativeDialogConfig>>();
        Emit<ProtobufConfigResolver<AudioMixerConfig>>();
        Emit<ProtobufConfigResolver<DeviceGradingConfig>>();
        Emit<ProtobufConfigResolver<CountriesTiersConfig>>();
        Emit<ProtobufConfigResolver<NotificationsMetaConfig>>();
        Emit<ProtobufConfigResolver<LevelAdsConfig>>();
        Emit<FlatBufferLazyConfigResolver<SystemConfigDict, SystemConfig, SystemConfigT>>();
        Emit<ProtobufConfigResolver<LevelAssistWeightsConfig>>();
        Emit<ProtobufConfigResolver<GameEventConfig>>();
        Emit<ProtobufConfigResolver<GameEventMetaConfig>>();
        Emit<FlatBufferLazyConfigResolver<CompetitionGameEventConfigDict, CompetitionGameEventConfig, CompetitionGameEventConfigT>>();

        Emit<ProtobufConfigResolver<DailyTriviaConfig>>();
        Emit<FlatBufferLazyConfigResolver<ChallengeTriviaRewardsConfigDict, ChallengeTriviaRewardsConfig, ChallengeTriviaRewardsConfigT>>();
        Emit<FlatBufferGreedyConfigResolver<ChallengeTriviaConfigDict, ChallengeTriviaConfig, ChallengeTriviaConfigT>>();
        Emit<FlatBufferLazyConfigResolver<ChallengeConfigDict, ChallengeConfig, ChallengeConfigT>>();
        Emit<ProtobufConfigResolver<MiscConfig>>();
        Emit<FlatBufferLazyConfigResolver<UnifiedPromotionConfigDict, UnifiedPromotionConfig, UnifiedPromotionConfigT>>();
        Emit<ProtobufConfigResolver<IAPBasketConfig>>();
        Emit<FlatBufferLazyConfigResolver<VIPProductsConfgDict, VIPProductsConfg, VIPProductsConfgT>>();
        Emit<FlatBufferLazyConfigResolver<VIPProductsMetaConfgDict, VIPProductsMetaConfg, VIPProductsMetaConfgT>>();
        Emit<FlatBufferLazyConfigResolver<ThemeConfigDict, ThemeConfig, ThemeConfigT>>();
        Emit<FlatBufferLazyConfigResolver<ThemeMetaConfigDict, ThemeMetaConfig, ThemeMetaConfigT>>();
        Emit<FlatBufferLazyConfigResolver<DefaultNamesConfigDict, DefaultNamesConfig, DefaultNamesConfigT>>();
        Emit<FlatBufferLazyConfigResolver<SocialConfigDict, SocialConfig, SocialConfigT>>();
        Emit<FlatBufferLazyConfigResolver<EndlessTreasureConfigDict, EndlessTreasureConfig, EndlessTreasureConfigT>>();
        Emit<ProtobufConfigResolver<RaceGameEventConfig>>();
        Emit<FlatBufferLazyConfigResolver<RaceStageConfigDict, RaceStageConfig, RaceStageConfigT>>();
        Emit<ProtobufConfigResolver<RoyaleGameEventConfig>>();
        Emit<FlatBufferLazyConfigResolver<TeamEventConfigDict, TeamEventConfig, TeamEventConfigT>>();
        Emit<FlatBufferLazyConfigResolver<PlayerSkillConfigDict, PlayerSkillConfig, PlayerSkillConfigT>>();
        Emit<FlatBufferConfigResolver<IceBreakerConfigDict, IceBreakerConfig, IceBreakerConfigT>>();
        Emit<FlatBufferLazyConfigResolver<WeeklyLeaderboardConfigDict, WeeklyLeaderboardConfig, WeeklyLeaderboardConfigT>>();
        Emit<FlatBufferLazyConfigResolver<AdPlacementConfigDict, AdPlacementConfig, AdPlacementConfigT>>();
        Emit<FlatBufferLazyConfigResolver<ButlerGiftConfigDict, ButlerGiftConfig, ButlerGiftConfigT>>();
        Emit<FlatBufferConfigResolver<AutoPopupPriorityConfigDict, AutoPopupPriorityConfig, AutoPopupPriorityConfigT>>();
        Emit<FlatBufferConfigResolver<HudAssetReferenceConfigDict, HudAssetReferenceConfig, HudAssetReferenceConfigT>>();
        Emit<FlatBufferConfigResolver<HudConfigDict, HudConfig, HudConfigT>>();
        Emit<FlatBufferGreedyConfigResolver<CollectionSetConfigDict, CollectionSetConfig, CollectionSetConfigT>>();
        Emit<FlatBufferGreedyConfigResolver<CollectionCardsConfigDict, CollectionCardsConfig, CollectionCardsConfigT>>();
        Emit<FlatBufferLazyConfigResolver<AssistSystemConfigDict, AssistSystemConfig, AssistSystemConfigT>>();
        Emit<FlatBufferConfigResolver<TutorialConfigDict, TutorialConfig, TutorialConfigT>>();
        Emit<FlatBufferGreedyConfigResolver<CollectionConfigDict, CollectionConfig, CollectionConfigT>>();
        Emit<FlatBufferLazyConfigResolver<SceneTaskConfigDict, SceneTaskConfig, SceneTaskConfigT>>();
        Emit<FlatBufferLazyConfigResolver<ScenesConfigDict, ScenesConfig, ScenesConfigT>>();
        Emit<FlatBufferGreedyConfigResolver<DailyTasksConfigDict, DailyTasksConfig, DailyTasksConfigT>>();
        Emit<FlatBufferLazyConfigResolver<DailyTaskSettingsConfigDict, DailyTaskSettingsConfig, DailyTaskSettingsConfigT>>();
        Emit<FlatBufferConfigResolver<ChallengeLocationConfigDict, ChallengeLocationConfig, ChallengeLocationConfigT>>();
        Emit<FlatBufferLazyConfigResolver<QuickActionsConfigDict, QuickActionsConfig, QuickActionsConfigT>>();
        
        Emit<FlatBufferLazyConfigResolver<CarrotsConfigDict, CarrotsConfig, CarrotsConfigT>>();
        Emit<FlatBufferLazyConfigResolver<SdbConfigDict, SdbConfig, SdbConfigT>>();
        
        Emit<FlatBufferConfigResolver<SweepstakesDailyLoginConfigDict, SweepstakesDailyLoginConfig, SweepstakesDailyLoginConfigT>>();
        Emit<FlatBufferLazyConfigResolver<SweepStakesGameEventConfigDict, SweepStakesGameEventConfig, SweepStakesGameEventConfigT>>();
        Emit<FlatBufferLazyConfigResolver<SweepstakesVideoConfigDict, SweepstakesVideoConfig, SweepstakesVideoConfigT>>();

        Emit<FlatBufferGreedyConfigResolver<GiantPinataOutcomeConfigDict, GiantPinataOutcomeConfig, GiantPinataOutcomeConfigT>>();
        Emit<FlatBufferLazyConfigResolver<ProfileCustomizationConfigDict, ProfileCustomizationConfig, ProfileCustomizationConfigT>>();
        
        sb.AppendLine("         };")
            .AppendLine()
            .AppendLine("         public static IReadOnlyList<IConfigResolver> GetConfigTypesToLoad() => AllResolvers;")
            .AppendLine("   }")
            .AppendLine("}");

        Directory.CreateDirectory(OutputFolder);
        File.WriteAllText(Path.Combine(OutputFolder, OutputFile), sb.ToString());
        AssetDatabase.Refresh();
        Debug.Log("[ResolverRegistry] Generated entries.");
        return;

        void Emit<T>() 
        {
            var csName = GetCSharpTypeName(typeof(T));
            sb.AppendLine($"            {csName}.Instance,");
        }

        string GetCSharpTypeName(Type t)
        {
            var baseName = t.IsGenericType ? t.Name[..t.Name.IndexOf('`')] : t.Name;

            var qualifier = importedNamespaces.Contains(t.Namespace) ? string.Empty : $"{t.Namespace}.";

            if (!t.IsGenericType)
            {
                return qualifier + baseName;
            }

            var args = t.GetGenericArguments().Select(GetCSharpTypeName);
            return qualifier + baseName + "<" + string.Join(", ", args) + ">";
        }
    }
}