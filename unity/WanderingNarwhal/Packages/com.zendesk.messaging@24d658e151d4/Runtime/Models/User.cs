using System;
using Newtonsoft.Json;
using Zendesk.MessagingCore.Models;

namespace Zendesk.Runtime.Models
{
    public class User
    {
        [JsonProperty("id")]
        internal string Id { get; set; }

        [JsonProperty("locale")]
        internal string Locale { get; set; }

        [JsonProperty("userId")]
        internal string ExternalId { get; set; }

        [JsonProperty("conversations")]
        internal Conversation[] Conversations { get; set; }

        [JsonProperty("realtimeSettings")]
        internal RealtimeSettings RealtimeSettings { get; set; }

        [JsonProperty("authentication")] 
        internal Authentication Auth { get; set; }
    }

    internal static class UserExtensions
    {
        internal static Conversation GetMostRecentConversation(this User user)
        {
            try
            {
                Conversation[] conversations = user.Conversations;
                if (conversations.Length == 0)
                    return null;
                
                Conversation mostRecentConversation = conversations[0];
                var mostRecentUpdatedAt = mostRecentConversation.lastUpdatedAt;

                for (int i = 0; i < conversations.Length; i++)
                {
                    if (conversations[i].lastUpdatedAt > mostRecentUpdatedAt)
                    {
                        mostRecentConversation = conversations[i];
                        mostRecentUpdatedAt = mostRecentConversation.lastUpdatedAt;
                    }
                }

                return mostRecentConversation;
            }
            catch (Exception)
            {
                return null;
            }
        }
        
        internal static User CopyAndUpdate(this User user, Authentication authentication)
        {
            return new User
            {
                Id = user.Id,
                Locale = user.Locale,
                ExternalId = user.ExternalId,
                Conversations = user.Conversations,
                RealtimeSettings = user.RealtimeSettings,
                Auth = authentication
            };
        }
        
        internal static bool IsAuthenticated(this User user) => user.Auth?.type == AuthType.jwt && user.Auth?.token != null;
        internal static bool IsAnonymous(this User user) => user.Auth?.type == AuthType.sessionToken && user.Auth?.token != null;
    }
}