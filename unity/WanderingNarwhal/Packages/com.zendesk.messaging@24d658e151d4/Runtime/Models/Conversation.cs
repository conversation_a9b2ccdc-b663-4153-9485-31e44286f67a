using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Zendesk.MessagingCore.Messaging.DTO;
using Zendesk.Runtime.Messaging;

namespace Zendesk.Runtime.Models
{
    public class Conversation
    {
        [JsonProperty] internal string id;
        [JsonProperty] internal string type;
        [JsonProperty] internal bool isDefault;
        [JsonProperty] internal string displayName;
        [JsonProperty] internal string displayDescription;
        [JsonProperty] internal string iconUrl;
        [JsonProperty] internal string[] business;
        [JsonProperty] internal double businessLastRead;
        [JsonProperty] internal double lastUpdatedAt;
        [JsonProperty] internal Participant[] participants;
        [JsonProperty] internal List<Message> messages;
        [JsonProperty] internal bool hasPrevious;
        [JsonProperty] internal Participant myself;
        [JsonProperty] internal Dictionary<string, object> metadata;
        // internal Activity activity;
    }

    internal static class ConversationExtensions
    {
        internal static void SetAllUserMessagesToSentState(this Conversation conversation)
        {
            if (conversation.messages == null || conversation.messages.Count == 0)
                return;

            conversation.messages
                .Where(message => message.Author.Role == MessageRole.User).ToList()
                .ForEach(message => message.State = MessageState.Sent);
        }
        
        internal static void RegisterUnreadMessageEvent(this Conversation conversation)
        {
            if (conversation?.participants == null || conversation.participants.Length == 0) 
                return;
            
            Participant participant = conversation.participants[0];
            var unreadCount = participant?.unreadCount ?? 0;
            UnreadMessageCount.UpdateFromConversation(conversation.id, unreadCount);
        }
    }
}