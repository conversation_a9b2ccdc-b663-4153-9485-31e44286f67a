using Zendesk.MessagingCore.Guide.Application.DTOs;
using Zendesk.Runtime.UI.Guide.Core.MVVM.Base;
using Zendesk.Runtime.UI.Guide.Core.MVVM.Commands;
using Zendesk.Runtime.UI.Guide.Core.Navigation.Enums;
using Zendesk.Runtime.UI.Guide.Core.Navigation.Interfaces;

namespace Zendesk.Runtime.UI.Guide.Screens.CategoryScreen.ViewModels
{
    internal class CategorySectionArticleViewModel : ViewModelBase, IListItemViewModel
    {
        public long Id { get; }
        public string Title { get; }
        public bool ShowSeparator { get; }
        public bool IsPromoted { get; }
        public IAsyncCommand NavigateToArticleCommand { get; }

        public CategorySectionArticleViewModel(
            INavigationService navigationService,
            ArticleDto articleDto) 
            : base(navigationService)
        {
            Id = articleDto.Id;
            Title = articleDto.Title;
            IsPromoted = articleDto.IsPromoted;
            ShowSeparator = true;

            NavigateToArticleCommand = new AsyncCommand(
                () => NavigationService.NavigateTo(GuideScreen.Article, Id)
            );
        }
    }
}