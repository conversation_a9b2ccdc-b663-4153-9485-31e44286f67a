using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Zendesk.MessagingCore.Logging;
using Zendesk.Runtime.Models;
using Zendesk.Runtime.UI.Scripts;
using Zendesk.Runtime.UI.Utilities;
using Logger = Zendesk.MessagingCore.Logging.Logger;

namespace Zendesk.Runtime.UI.Messaging.WidgetComponents
{
    public class CarouselItemComponent : MonoBehaviour
    {
        [Header("Image Components")] [SerializeField]
        private CarouselImageComponent _imageComponent;

        [SerializeField] private Image _carouselBackgroundImage;

        [Header("Text Components")] [SerializeField]
        private TMP_Text _title;

        [SerializeField] private RectTransform _titleRectTransform;
        [SerializeField] private RectTransform _descriptionRectTransform;

        [SerializeField] private TMP_Text _description;

        [Header("Button Components")] [SerializeField]
        private List<ButtonLinkComponent> _buttonLinks = new List<ButtonLinkComponent>(3);

        [SerializeField] private ZMTheme _zmTheme;

        [Header("Rect Transforms")] [SerializeField]
        private RectTransform _imageTextContainerRectTransform;

        [SerializeField] private RectTransform _buttonContainerRectTransform;
        [SerializeField] private RectTransform _rectTransform;
        [SerializeField] private RectTransform _textContainer;
        [SerializeField] private RectTransform _cardRectTransform;

        [Header("Values")] [SerializeField] private float _maxTitleDescriptionHeight = 180f;
        [SerializeField] private float _maxCardWidth = 1752f;
        [SerializeField] private float _leftCardPadding = 156f;
        [SerializeField] private float _rightCardPadding = 192f;

        private ITextureHandle _textureHandle;
        private readonly Logger _logger = LoggerManager.GetLogger<CarouselImageComponent>();

        public float CardHeight => _rectTransform.rect.height;
        public Vector2 CardPosition => _rectTransform.anchoredPosition;

        public bool IsValid => _imageComponent != null && _carouselBackgroundImage != null && _title != null &&
                               _titleRectTransform != null && _descriptionRectTransform != null &&
                               _description != null && _buttonLinks != null && _zmTheme != null &&
                               _buttonContainerRectTransform != null && _rectTransform != null &&
                               _textContainer != null && _cardRectTransform && _imageComponent.IsValid;

        public void LoadItemData(MessageArticleSuggestion carouselItemData)
        {
            int buttonCount = carouselItemData.Actions.Count;
            if (buttonCount > 3) buttonCount = 3;

            _title.text = carouselItemData.Title;
            _description.text = carouselItemData.Description;

            if (!string.IsNullOrEmpty(carouselItemData.MediaUrl))
            {
                _imageComponent.LoadAbsentImageState();
            }

            if (carouselItemData.Actions == null || carouselItemData.Actions.Count < 1 ||
                carouselItemData.Actions.Count > 3)
            {
                _logger.LogError($"Carousel contains an invalid actions list: {carouselItemData.Actions}");
                return;
            }

            for (var index = 0; index < buttonCount; index++)
            {
                ButtonLinkComponent buttonLinkComponent = _buttonLinks[index];
                buttonLinkComponent.gameObject.SetActive(true);
                buttonLinkComponent.SetThemeColors(_zmTheme);
                _buttonLinks[index].ConfigureButtonLinkAction(carouselItemData.Actions[index]);
            }
        }

        private void UpdateText(TMP_Text textElement, RectTransform rectTransform)
        {
            textElement.overflowMode = TextOverflowModes.Overflow;
            textElement.ForceMeshUpdate();
            if (textElement.textInfo.lineCount < 4)
            {
                rectTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, textElement.GetRenderedValues().y);
                textElement.ForceMeshUpdate();
            }
            else if (textElement.textInfo.lineCount >= 4)
            {
                textElement.overflowMode = TextOverflowModes.Ellipsis;
                float height = textElement.GetPreferredValues().y;
                if (height > _maxTitleDescriptionHeight)
                {
                    height = _maxTitleDescriptionHeight;
                }

                rectTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, height);
                textElement.ForceMeshUpdate();
            }
        }

        #region Image Logic

        public void SetImage(ITextureHandle textureHandle)
        {
            _imageComponent.SetImage(textureHandle);
        }

        public void EnableImageComponent()
        {
            _imageComponent.gameObject.SetActive(true);
        }

        public void ResetCard()
        {
            _imageComponent.ResetComponent();
            _imageComponent.gameObject.SetActive(false);

            _title.text = String.Empty;
            _description.text = String.Empty;

            foreach (ButtonLinkComponent buttonComponent in _buttonLinks)
            {
                buttonComponent.Release();
                buttonComponent.gameObject.SetActive(false);
            }
        }

        #endregion


        public void UpdateCardElements(float screenWidth)
        {
            if (screenWidth > _maxCardWidth + _leftCardPadding + _rightCardPadding)
            {
                _cardRectTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, _maxCardWidth);
            }
            else
            {
                _cardRectTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal,
                    screenWidth - _leftCardPadding - _rightCardPadding);
            }

            // Update the layout elements before resizing the card
            LayoutRebuilder.ForceRebuildLayoutImmediate(_textContainer);
            UpdateText(_title, _titleRectTransform);
            UpdateText(_description, _descriptionRectTransform);
            LayoutRebuilder.ForceRebuildLayoutImmediate(_textContainer);
            LayoutRebuilder.ForceRebuildLayoutImmediate(_imageTextContainerRectTransform);
            LayoutRebuilder.ForceRebuildLayoutImmediate(_buttonContainerRectTransform);

            _rectTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical,
                _buttonContainerRectTransform.rect.height + _imageTextContainerRectTransform.rect.height);
        }

        public void UpdateTheme()
        {
            _carouselBackgroundImage.color = _zmTheme.SystemMessageColor;
        }

        public void ResizeCard(float newHeight)
        {
            _rectTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, newHeight);
        }
    }
}