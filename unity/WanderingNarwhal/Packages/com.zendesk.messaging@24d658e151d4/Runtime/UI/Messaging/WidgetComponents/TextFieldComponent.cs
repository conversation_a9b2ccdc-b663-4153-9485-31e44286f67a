using Zendesk.Runtime.Models;

namespace Zendesk.Runtime.UI.Messaging.WidgetComponents
{
    public class TextFieldComponent : BaseTextFieldComponent
    {
        private const string _FieldValidationErrorMessageKey = "messaging_unity_zmu_form_field_required_label";
        public override MessageFormFieldType FieldType => MessageFormFieldType.Text;
        protected override string ErrorTextKey => _FieldValidationErrorMessageKey;
        public override bool IsUserInputValid => !string.IsNullOrWhiteSpace(_userInputData.Text);
        protected override string GetTextInputFromFormField(MessageFormField messageFormField) => messageFormField.Text;
    }
}
