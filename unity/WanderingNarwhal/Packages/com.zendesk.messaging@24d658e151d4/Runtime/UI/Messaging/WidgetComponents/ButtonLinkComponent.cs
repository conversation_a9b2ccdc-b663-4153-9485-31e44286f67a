using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Zendesk.Runtime.UI.Article;
using Zendesk.Runtime.UI.Scripts;
using Zendesk.Runtime.UI.Utilities.ClickActionHandlers;

namespace Zendesk.Runtime.UI.Messaging.WidgetComponents
{
    public interface IButtonLinkComponent
    {
        void TryCreateButtonLink(string buttonText, string actionText, IClickActionHandler clickActionHandler);
        void CreateOptionNotUnsupportedButtonLink();
        public IZMLinkHandler LinkHandlerComponent { get; }
    }

    public class ButtonLinkComponent : MonoBehaviour, IButtonLinkComponent
    {
        [SerializeField] private ZMLinkHandler _linkHandler;
        [SerializeField] private ZMTextTranslator _textTranslator;

        [Header("UI Components")]
        [SerializeField] internal TMP_Text _buttonText;
        [SerializeField] internal Button _button;

        [SerializeField] private string _translationKey = "messaging_unity_zmu_message_action_option_not_supported_label";

        public bool IsValid =>
            _button != null &&
            _buttonText != null;

        public void TryCreateButtonLink(string buttonText, string actionText, IClickActionHandler clickActionHandler)
        {
            _buttonText.text = buttonText;
            var additionalParams = new Dictionary<string, object> { { nameof(buttonText), buttonText } };
            _button.onClick.AddListener(() => { clickActionHandler.OnClick(actionText, additionalParams); });
        }

        public void CreateOptionNotUnsupportedButtonLink()
        {
            _button.interactable = false;
            _textTranslator.ChangeKeyAndSetText(_translationKey);
        }

        public IZMLinkHandler LinkHandlerComponent => _linkHandler;

        public void SetThemeColors(ZMTheme theme)
        {
            ColorBlock buttonColors = _button.colors;
            buttonColors.normalColor = theme.ActionColor;
            buttonColors.highlightedColor = theme.ButtonLinkActionPressedColor;
            buttonColors.pressedColor = theme.ButtonLinkActionPressedColor;
            buttonColors.selectedColor = theme.ActionColor;
            buttonColors.disabledColor = theme.ButtonLinkActionNotSupportedColor;
            _button.colors = buttonColors;
        }

        public void Release()
        {
            // Clear all information from this button link component
            _button.onClick.RemoveAllListeners();
            _button.interactable = true;
            _buttonText.text = string.Empty;
        }
    }
}