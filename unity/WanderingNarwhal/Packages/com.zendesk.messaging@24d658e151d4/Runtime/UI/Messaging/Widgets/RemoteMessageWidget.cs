using UnityEngine;
using UnityEngine.UI;
using Zendesk.Runtime.Messaging;
using Zendesk.Runtime.Models;
using Zendesk.Runtime.UI.Messaging.Contexts;
using Zendesk.Runtime.UI.Messaging.WidgetComponents;
using Zendesk.Runtime.UI.Scripts;
using Zendesk.Runtime.UI.Utilities;
using Zendesk.Runtime.UI.Widgets;

namespace Zendesk.Runtime.UI.Messaging.Widgets
{
    public abstract class RemoteMessageWidget<T> : Widget<T> where T : RemoteMessageWidgetContext
    {
        private const string _TranslationKeyJustNow = "messaging_unity_zmu_conversation_message_label_just_now";
        
        [SerializeField] private ZMTheme _theme;
        [Header("Default Components")]
        [SerializeField] private AvatarComponent _avatarComponent;
        [SerializeField] private TitleComponent _userNameComponent;
        [SerializeField] private ReceiptComponent _receiptComponent;
        [SerializeField] private LayoutGroup _layoutGroup;

        protected ZMTheme Theme => _theme;
        protected ReceiptComponent ReceiptComponent => _receiptComponent;

        public override bool IsWidgetConfigurationValid()
        {
            return
                _theme != null && _layoutGroup != null &&
                _avatarComponent != null && _avatarComponent.IsValid &&
                _userNameComponent != null && _userNameComponent.IsValid &&
                _receiptComponent != null && _receiptComponent.IsValid;
        }
        
        protected override void OnBindContext(out bool wasSuccess)
        {
            Context.MessageUpdatedEvent += OnMessageUpdatedEvent;
            Context.LayoutUpdatedEvent += OnLayoutUpdatedEvent;

            _receiptComponent.SetVisibility(Context.IsReceiptVisible);

            _avatarComponent.SetVisibility(Context.IsAvatarVisible);
            _avatarComponent.SetAvatar(Context.AvatarHandle);

            _userNameComponent.SetText(Context.UserName);
            _userNameComponent.SetVisibility(Context.IsTitleVisible);
            _layoutGroup.padding.bottom = ComponentUtilities.GetTrailingSpace(Context.HasTrailingSpace, ZMTheme.WidgetTrailingSpace);

            wasSuccess = true;
        }

        protected override void OnReleaseContext()
        {
            Context.MessageUpdatedEvent -= OnMessageUpdatedEvent;
            Context.LayoutUpdatedEvent -= OnLayoutUpdatedEvent;
            
            _avatarComponent.ReleaseAvatar();
        }
        
        protected virtual void OnMessageUpdatedEvent(Message message)
        {
            UpdateReceipt();
        }

        protected virtual void OnLayoutUpdatedEvent(LayoutData layoutData)
        {
            UpdateReceipt();
            _avatarComponent.SetVisibility(Context.IsAvatarVisible);
            _userNameComponent.SetVisibility(Context.IsTitleVisible);
            _layoutGroup.padding.bottom = ComponentUtilities.GetTrailingSpace(Context.HasTrailingSpace, ZMTheme.WidgetTrailingSpace);
        }
        
        protected virtual void UpdateReceipt()
        {
            _receiptComponent.SetVisibility(Context.IsReceiptVisible);
            _receiptComponent.ApplyStyle(_receiptComponent.DefaultTextColor, _receiptComponent.DefaultSpriteColor);
            _receiptComponent.SetText(_TranslationKeyJustNow);
        }
    }
}
