using UnityEngine;

namespace Zendesk.Runtime.UI.Utilities
{
    internal class GameObjectWrapper : IGameObjectWrapper
    {
        public GameObject Instantiate(GameObject prefab, Transform transform = null) => Object.Instantiate(prefab, transform);
        public void Destroy(GameObject gameObject) => Object.Destroy(gameObject);
        public void DestroyImmediately(GameObject gameObject) => Object.DestroyImmediate(gameObject);
        public void SetActive(GameObject gameObject, bool enable) => gameObject.SetActive(enable);
    }
}