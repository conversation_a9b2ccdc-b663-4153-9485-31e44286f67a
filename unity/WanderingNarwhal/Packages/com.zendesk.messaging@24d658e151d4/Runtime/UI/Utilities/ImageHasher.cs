using System;
using Zendesk.Runtime.Utils.FileUtils;

namespace Zendesk.Runtime.UI.Utilities
{
    /// <summary>
    /// Class responsible for hashing images.
    /// </summary>
    internal class ImageHasher
    {
        private readonly IFile _file;

        /// <summary>
        /// Default constructor.
        /// </summary>
        public ImageHasher() : this(new FileWrapper())
        {
        }
        
        /// <summary>
        /// Secondary constructor.
        /// </summary>
        public ImageHasher(IFile file)
        {
            _file = file;
        }
        
        /// <summary>
        /// Gets the hash of a local image file.
        /// </summary>
        /// <param name="path">The path to the image.</param>
        /// <param name="byteCount">The number of initial bytes to read for hashing.</param>
        /// <returns>A string representing the hash of the image. (null if not found or exception)</returns>
        public string GetLocalImageHash(string path, int byteCount)
        {
            if (!IsLocalImage(path))
                return null;
            
            try
            {
                var fileInfo = _file.FileInfo(path);
                return GetCombinedHash(path, fileInfo.Length, byteCount);
            }
            catch (Exception)
            {
                return null;
            }
        }
        
        private static bool IsLocalImage(string uri) => !uri.ToLower().StartsWith("http");

        private string GetCombinedHash(string filePath, long fileSize, int initialByteCount)
        {
            var initialBytes = GetBytes(filePath, initialByteCount);
            return $"{fileSize}-{BitConverter.ToString(initialBytes)}";
        }

        private byte[] GetBytes(string filePath, int count)
        {
            var bytes = new byte[count];
            using (var fileStream = _file.OpenRead(filePath))
            {
                _ = fileStream.Read(bytes, 0, count);
            }
            return bytes;
        }
    }
}