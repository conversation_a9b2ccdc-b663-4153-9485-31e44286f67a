using System.Collections.Generic;

namespace Zendesk.MessagingCore.RestClient.Authenticator
{
    internal class JwtAuthenticator : IAuthenticator
    {
        private readonly string _jwt;

        public JwtAuthenticator(string jwt)
        {
            _jwt = jwt;
        }

        public KeyValuePair<string, string> GenerateHeader()
        {
            return new KeyValuePair<string, string>("Authorization", $"Bearer {_jwt}");
        }
    }
}