using Zendesk.MessagingCore.Cache;
using Zendesk.MessagingCore.RestClient.Client;
using Zendesk.Runtime.Environment;

namespace Zendesk.MessagingCore.Environment
{
    internal interface IEnv
    {
        IPlatform Platform { get; }
        IZMRestBuilder ZMRestClientBuilder { get; }
        IMessagingCoreEvents MessagingCoreEvents { get; }
        IAppDetails AppDetails { get; }
        public IRestClientWrapper RestClientWrapper { get; }
        public ICache Cache { get; }
    }

    internal class Env : IEnv
    {
        public static IEnv Current
        {
            get;
            internal set;
        }

        public IPlatform Platform { get; private set; }
        public IZMRestBuilder ZMRestClientBuilder { get; private set; }
        public IAppDetails AppDetails { get; private set; }
        public IMessagingCoreEvents MessagingCoreEvents { get; private set; }
        public IRestClientWrapper RestClientWrapper { get; private set; }
        public ICache Cache { get; private set; }

        public static void Initialize(
            IPlatform platform,
            IMessagingCoreEvents messagingCoreEvents,
            IAppDetails appDetails,
            IRestClientWrapper restClientWrapper,
            ICache cache = null)
        {
            var environment = new Env
            {
                Platform = platform,
                ZMRestClientBuilder = new ZMRestBuilder(),
                MessagingCoreEvents = messagingCoreEvents,
                AppDetails = appDetails,
                RestClientWrapper = restClientWrapper,
                Cache = cache ?? new MemoryCache()
            };
            Current = environment;
        }

        public static void Invalidate()
        {
            Current = null;
        }
    }
}
