using System.Collections.Generic;
using Newtonsoft.Json;
using Zendesk.MessagingCore.Guide.Domain.Entities;

namespace Zendesk.MessagingCore.Guide.Infrastructure.HttpRepositories.DTOs
{
    internal class CategoriesResponseDto : PaginationDto
    {
        [JsonProperty("categories")] internal List<CategoryResponseDto> _categories;

        public IEnumerable<CategoryEntity> ToDomainCategories()
        {
            var categories = new List<CategoryEntity>();

            foreach (CategoryResponseDto category in _categories)
            {
                categories.Add(category.ToCategoryEntity());
            }

            return categories;
        }
    }
}